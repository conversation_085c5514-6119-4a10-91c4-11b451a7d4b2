#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/projects/smatflow/civicpoll/node_modules/.pnpm/@strapi+strapi@5.16.1_@babel+runtime@7.27.6_@codemirror+autocomplete@6.18.6_@codemirror_63415d5a934a67d48e776f6b7cf5d67e/node_modules/@strapi/strapi/bin/node_modules:/home/<USER>/projects/smatflow/civicpoll/node_modules/.pnpm/@strapi+strapi@5.16.1_@babel+runtime@7.27.6_@codemirror+autocomplete@6.18.6_@codemirror_63415d5a934a67d48e776f6b7cf5d67e/node_modules/@strapi/strapi/node_modules:/home/<USER>/projects/smatflow/civicpoll/node_modules/.pnpm/@strapi+strapi@5.16.1_@babel+runtime@7.27.6_@codemirror+autocomplete@6.18.6_@codemirror_63415d5a934a67d48e776f6b7cf5d67e/node_modules/@strapi/node_modules:/home/<USER>/projects/smatflow/civicpoll/node_modules/.pnpm/@strapi+strapi@5.16.1_@babel+runtime@7.27.6_@codemirror+autocomplete@6.18.6_@codemirror_63415d5a934a67d48e776f6b7cf5d67e/node_modules:/home/<USER>/projects/smatflow/civicpoll/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/projects/smatflow/civicpoll/node_modules/.pnpm/@strapi+strapi@5.16.1_@babel+runtime@7.27.6_@codemirror+autocomplete@6.18.6_@codemirror_63415d5a934a67d48e776f6b7cf5d67e/node_modules/@strapi/strapi/bin/node_modules:/home/<USER>/projects/smatflow/civicpoll/node_modules/.pnpm/@strapi+strapi@5.16.1_@babel+runtime@7.27.6_@codemirror+autocomplete@6.18.6_@codemirror_63415d5a934a67d48e776f6b7cf5d67e/node_modules/@strapi/strapi/node_modules:/home/<USER>/projects/smatflow/civicpoll/node_modules/.pnpm/@strapi+strapi@5.16.1_@babel+runtime@7.27.6_@codemirror+autocomplete@6.18.6_@codemirror_63415d5a934a67d48e776f6b7cf5d67e/node_modules/@strapi/node_modules:/home/<USER>/projects/smatflow/civicpoll/node_modules/.pnpm/@strapi+strapi@5.16.1_@babel+runtime@7.27.6_@codemirror+autocomplete@6.18.6_@codemirror_63415d5a934a67d48e776f6b7cf5d67e/node_modules:/home/<USER>/projects/smatflow/civicpoll/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@strapi/strapi/bin/strapi.js" "$@"
else
  exec node  "$basedir/../@strapi/strapi/bin/strapi.js" "$@"
fi

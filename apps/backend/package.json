{"name": "backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "5.16.1", "@strapi/plugin-users-permissions": "5.16.1", "@strapi/strapi": "5.16.1", "@strapi/provider-email-sendgrid": "5.16.1", "pg": "^8.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "helmet": "^7.0.0", "express-rate-limit": "^7.0.0", "ioredis": "^5.3.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "4a6911a7a13dd95b0e0a25d1146ff21e9ce88c766124f1fc1c4ca6cdf08158c3"}}
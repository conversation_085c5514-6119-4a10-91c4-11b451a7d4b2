export default ({ env }) => ({
  // Email provider configuration
  email: {
    config: {
      provider: 'sendgrid',
      providerOptions: {
        apiKey: env('EMAIL_SENDGRID_API_KEY'),
      },
      settings: {
        defaultFrom: '<EMAIL>',
        defaultReplyTo: '<EMAIL>',
        testAddress: '<EMAIL>',
      },
    },
  },

  // Upload provider configuration
  upload: {
    config: {
      provider: 'local',
      providerOptions: {
        sizeLimit: 50 * 1024 * 1024, // 50MB
      },
    },
  },

  // GDPR plugin configuration
  gdpr: {
    enabled: true,
    resolve: './src/plugins/gdpr',
    config: {
      dataRetentionDays: env.int('GDPR_DATA_RETENTION_DAYS', 365),
      anonymizeOnDelete: env.bool('GDPR_ANONYMIZE_ON_DELETE', true),
      auditEnabled: env.bool('GDPR_AUDIT_ENABLED', true),
    },
  },

  // Users permissions plugin configuration
  'users-permissions': {
    config: {
      jwt: {
        expiresIn: '7d',
      },
      register: {
        allowedFields: ['firstName', 'lastName', 'phone'],
      },
    },
  },

  // Note: i18n plugin not yet available for Strapi v5
  // Will be added in future updates
});

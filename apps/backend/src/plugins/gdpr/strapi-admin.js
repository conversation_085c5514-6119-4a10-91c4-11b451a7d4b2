import { prefixPluginTranslations } from '@strapi/helper-plugin';

const name = 'gdpr';

export default {
  register(app) {
    app.addMenuLink({
      to: `/plugins/${name}`,
      icon: 'shield',
      intlLabel: {
        id: `${name}.plugin.name`,
        defaultMessage: 'GDPR',
      },
      Component: async () => {
        const component = await import('./admin/src/pages/App');
        return component;
      },
      permissions: [
        {
          action: 'plugin::gdpr.read',
          subject: null,
        },
      ],
    });

    app.registerPlugin({
      id: name,
      initializer: () => null,
      isReady: false,
      name,
    });
  },

  bootstrap(app) {},

  async registerTrads(app) {
    const { locales } = app;

    const importedTrads = await Promise.all(
      locales.map((locale) => {
        return import(`./admin/src/translations/${locale}.json`)
          .then(({ default: data }) => {
            return {
              data: prefixPluginTranslations(data, name),
              locale,
            };
          })
          .catch(() => {
            return {
              data: {},
              locale,
            };
          });
      })
    );

    return Promise.resolve(importedTrads);
  },
};

const register = ({ strapi }) => {
  // Register GDPR services
  strapi.plugin('gdpr').service('export');
  strapi.plugin('gdpr').service('anonymize');
  strapi.plugin('gdpr').service('audit');
  strapi.plugin('gdpr').service('consent');
};

const bootstrap = ({ strapi }) => {
  // Bootstrap GDPR functionality
  strapi.log.info('GDPR plugin initialized');
};

const destroy = ({ strapi }) => {
  // Cleanup when plugin is destroyed
};

const config = {
  default: {
    dataRetentionDays: 365,
    anonymizeOnDelete: true,
    auditEnabled: true,
  },
  validator: (config) => {
    if (typeof config.dataRetentionDays !== 'number') {
      throw new Error('dataRetentionDays must be a number');
    }
    if (typeof config.anonymizeOnDelete !== 'boolean') {
      throw new Error('anonymizeOnDelete must be a boolean');
    }
    if (typeof config.auditEnabled !== 'boolean') {
      throw new Error('auditEnabled must be a boolean');
    }
  },
};

const contentTypes = {
  'gdpr-request': {
    schema: {
      kind: 'collectionType',
      collectionName: 'gdpr_requests',
      info: {
        name: 'GDPR Request',
        description: 'GDPR data export and deletion requests',
        singularName: 'gdpr-request',
        pluralName: 'gdpr-requests',
        displayName: 'GDPR Request',
      },
      options: {
        draftAndPublish: false,
        comment: '',
      },
      pluginOptions: {
        'content-manager': {
          visible: true,
        },
        'content-type-builder': {
          visible: false,
        },
      },
      attributes: {
        requestType: {
          type: 'enumeration',
          enum: ['export', 'delete'],
          required: true,
        },
        status: {
          type: 'enumeration',
          enum: ['pending', 'processing', 'completed', 'failed'],
          default: 'pending',
          required: true,
        },
        userId: {
          type: 'integer',
          required: true,
        },
        userEmail: {
          type: 'email',
          required: true,
        },
        requestedAt: {
          type: 'datetime',
          default: () => new Date(),
        },
        completedAt: {
          type: 'datetime',
        },
        data: {
          type: 'json',
        },
        notes: {
          type: 'text',
        },
        ipAddress: {
          type: 'string',
        },
        userAgent: {
          type: 'text',
        },
      },
    },
  },
  'audit-log': {
    schema: {
      kind: 'collectionType',
      collectionName: 'audit_logs',
      info: {
        name: 'Audit Log',
        description: 'System audit trail for GDPR compliance',
        singularName: 'audit-log',
        pluralName: 'audit-logs',
        displayName: 'Audit Log',
      },
      options: {
        draftAndPublish: false,
        comment: '',
      },
      pluginOptions: {
        'content-manager': {
          visible: true,
        },
        'content-type-builder': {
          visible: false,
        },
      },
      attributes: {
        action: {
          type: 'string',
          required: true,
        },
        userId: {
          type: 'integer',
        },
        userEmail: {
          type: 'email',
        },
        ipAddress: {
          type: 'string',
        },
        userAgent: {
          type: 'text',
        },
        details: {
          type: 'json',
        },
        timestamp: {
          type: 'datetime',
          default: () => new Date(),
        },
        entityType: {
          type: 'string',
        },
        entityId: {
          type: 'integer',
        },
      },
    },
  },
};

const routes = {
  admin: {
    type: 'admin',
    routes: [
      {
        method: 'GET',
        path: '/gdpr-requests',
        handler: 'gdpr.getRequests',
        config: {
          policies: ['admin::isAuthenticatedAdmin'],
        },
      },
      {
        method: 'POST',
        path: '/gdpr-requests',
        handler: 'gdpr.createRequest',
        config: {
          policies: ['admin::isAuthenticatedAdmin'],
        },
      },
      {
        method: 'GET',
        path: '/audit-logs',
        handler: 'audit.getLogs',
        config: {
          policies: ['admin::isAuthenticatedAdmin'],
        },
      },
    ],
  },
  'content-api': {
    type: 'content-api',
    routes: [
      {
        method: 'POST',
        path: '/gdpr/export',
        handler: 'gdpr.exportUserData',
        config: {
          policies: [],
          middlewares: [],
        },
      },
      {
        method: 'POST',
        path: '/gdpr/delete',
        handler: 'gdpr.deleteUserData',
        config: {
          policies: [],
          middlewares: [],
        },
      },
      {
        method: 'GET',
        path: '/gdpr/consent/:userId',
        handler: 'consent.getConsent',
        config: {
          policies: [],
          middlewares: [],
        },
      },
      {
        method: 'POST',
        path: '/gdpr/consent',
        handler: 'consent.updateConsent',
        config: {
          policies: [],
          middlewares: [],
        },
      },
    ],
  },
};

const controllers = require('./server/controllers');
const services = require('./server/services');
const policies = require('./server/policies');

module.exports = {
  register,
  bootstrap,
  destroy,
  config,
  controllers,
  services,
  policies,
  contentTypes,
  routes,
};

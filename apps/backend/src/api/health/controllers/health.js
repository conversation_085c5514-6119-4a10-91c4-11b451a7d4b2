module.exports = {
  /**
   * Health check endpoint
   */
  async check(ctx) {
    try {
      const startTime = Date.now();
      
      // Check database connection
      let dbStatus = 'healthy';
      let dbResponseTime = 0;
      try {
        const dbStart = Date.now();
        await strapi.db.connection.raw('SELECT 1');
        dbResponseTime = Date.now() - dbStart;
      } catch (error) {
        dbStatus = 'unhealthy';
        strapi.log.error('Database health check failed:', error);
      }

      // Check Redis connection (if configured)
      let redisStatus = 'not_configured';
      let redisResponseTime = 0;
      try {
        if (process.env.REDIS_HOST) {
          const redisStart = Date.now();
          // Add Redis health check here when Redis is implemented
          redisResponseTime = Date.now() - redisStart;
          redisStatus = 'healthy';
        }
      } catch (error) {
        redisStatus = 'unhealthy';
        strapi.log.error('Redis health check failed:', error);
      }

      const totalResponseTime = Date.now() - startTime;
      const isHealthy = dbStatus === 'healthy';

      const healthData = {
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        version: strapi.config.info.version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        checks: {
          database: {
            status: dbStatus,
            responseTime: `${dbResponseTime}ms`,
          },
          redis: {
            status: redisStatus,
            responseTime: redisStatus !== 'not_configured' ? `${redisResponseTime}ms` : null,
          },
        },
        responseTime: `${totalResponseTime}ms`,
      };

      // Set appropriate HTTP status
      ctx.status = isHealthy ? 200 : 503;
      ctx.body = healthData;

    } catch (error) {
      strapi.log.error('Health check failed:', error);
      ctx.status = 503;
      ctx.body = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        message: error.message,
      };
    }
  },

  /**
   * Metrics endpoint for Prometheus
   */
  async metrics(ctx) {
    try {
      const memUsage = process.memoryUsage();
      const uptime = process.uptime();
      
      // Basic metrics in Prometheus format
      const metrics = [
        '# HELP civicpoll_uptime_seconds Total uptime in seconds',
        '# TYPE civicpoll_uptime_seconds counter',
        `civicpoll_uptime_seconds ${uptime}`,
        '',
        '# HELP civicpoll_memory_usage_bytes Memory usage in bytes',
        '# TYPE civicpoll_memory_usage_bytes gauge',
        `civicpoll_memory_usage_bytes{type="rss"} ${memUsage.rss}`,
        `civicpoll_memory_usage_bytes{type="heapTotal"} ${memUsage.heapTotal}`,
        `civicpoll_memory_usage_bytes{type="heapUsed"} ${memUsage.heapUsed}`,
        `civicpoll_memory_usage_bytes{type="external"} ${memUsage.external}`,
        '',
        '# HELP civicpoll_nodejs_version Node.js version info',
        '# TYPE civicpoll_nodejs_version gauge',
        `civicpoll_nodejs_version{version="${process.version}"} 1`,
        '',
      ];

      // Add database metrics
      try {
        const dbStart = Date.now();
        await strapi.db.connection.raw('SELECT 1');
        const dbResponseTime = Date.now() - dbStart;
        
        metrics.push(
          '# HELP civicpoll_database_response_time_ms Database response time in milliseconds',
          '# TYPE civicpoll_database_response_time_ms gauge',
          `civicpoll_database_response_time_ms ${dbResponseTime}`,
          '',
          '# HELP civicpoll_database_status Database status (1=healthy, 0=unhealthy)',
          '# TYPE civicpoll_database_status gauge',
          'civicpoll_database_status 1',
          ''
        );
      } catch (error) {
        metrics.push(
          '# HELP civicpoll_database_status Database status (1=healthy, 0=unhealthy)',
          '# TYPE civicpoll_database_status gauge',
          'civicpoll_database_status 0',
          ''
        );
      }

      // Add GDPR metrics if available
      try {
        const pendingRequests = await strapi.db.query('plugin::gdpr.gdpr-request').count({
          where: { status: { $in: ['pending', 'processing'] } },
        });
        
        metrics.push(
          '# HELP civicpoll_gdpr_pending_requests Number of pending GDPR requests',
          '# TYPE civicpoll_gdpr_pending_requests gauge',
          `civicpoll_gdpr_pending_requests ${pendingRequests}`,
          ''
        );
      } catch (error) {
        // GDPR tables might not exist yet
      }

      ctx.set('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
      ctx.body = metrics.join('\n');

    } catch (error) {
      strapi.log.error('Metrics endpoint failed:', error);
      ctx.status = 500;
      ctx.body = '# Error generating metrics\n';
    }
  },
};

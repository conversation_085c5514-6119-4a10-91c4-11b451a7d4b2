{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.4", "axios": "^1.6.0", "swr": "^2.2.0", "framer-motion": "^11.0.0", "react-hook-form": "^7.48.0", "zod": "^3.22.0", "@hookform/resolvers": "^3.3.0", "next-themes": "^0.3.0", "lucide-react": "^0.263.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}
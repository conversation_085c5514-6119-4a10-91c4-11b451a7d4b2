import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const memUsage = process.memoryUsage();
    const uptime = process.uptime();
    
    // Basic metrics in Prometheus format
    const metrics = [
      '# HELP civicpoll_frontend_uptime_seconds Total uptime in seconds',
      '# TYPE civicpoll_frontend_uptime_seconds counter',
      `civicpoll_frontend_uptime_seconds ${uptime}`,
      '',
      '# HELP civicpoll_frontend_memory_usage_bytes Memory usage in bytes',
      '# TYPE civicpoll_frontend_memory_usage_bytes gauge',
      `civicpoll_frontend_memory_usage_bytes{type="rss"} ${memUsage.rss}`,
      `civicpoll_frontend_memory_usage_bytes{type="heapTotal"} ${memUsage.heapTotal}`,
      `civicpoll_frontend_memory_usage_bytes{type="heapUsed"} ${memUsage.heapUsed}`,
      `civicpoll_frontend_memory_usage_bytes{type="external"} ${memUsage.external}`,
      '',
      '# HELP civicpoll_frontend_nodejs_version Node.js version info',
      '# TYPE civicpoll_frontend_nodejs_version gauge',
      `civicpoll_frontend_nodejs_version{version="${process.version}"} 1`,
      '',
      '# HELP civicpoll_frontend_nextjs_version Next.js version info',
      '# TYPE civicpoll_frontend_nextjs_version gauge',
      `civicpoll_frontend_nextjs_version{version="${process.env.npm_package_dependencies_next || 'unknown'}"} 1`,
      '',
    ];

    // Add backend connectivity metrics
    try {
      const backendStart = Date.now();
      const backendUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337';
      const response = await fetch(`${backendUrl}/_health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000),
      });
      
      const backendResponseTime = Date.now() - backendStart;
      const backendStatus = response.ok ? 1 : 0;
      
      metrics.push(
        '# HELP civicpoll_frontend_backend_response_time_ms Backend response time in milliseconds',
        '# TYPE civicpoll_frontend_backend_response_time_ms gauge',
        `civicpoll_frontend_backend_response_time_ms ${backendResponseTime}`,
        '',
        '# HELP civicpoll_frontend_backend_status Backend connectivity status (1=healthy, 0=unhealthy)',
        '# TYPE civicpoll_frontend_backend_status gauge',
        `civicpoll_frontend_backend_status ${backendStatus}`,
        ''
      );
    } catch (error) {
      metrics.push(
        '# HELP civicpoll_frontend_backend_status Backend connectivity status (1=healthy, 0=unhealthy)',
        '# TYPE civicpoll_frontend_backend_status gauge',
        'civicpoll_frontend_backend_status 0',
        ''
      );
    }

    // Add environment info
    metrics.push(
      '# HELP civicpoll_frontend_environment Environment info',
      '# TYPE civicpoll_frontend_environment gauge',
      `civicpoll_frontend_environment{env="${process.env.NODE_ENV || 'development'}"} 1`,
      ''
    );

    return new NextResponse(metrics.join('\n'), {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; version=0.0.4; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Frontend metrics endpoint failed:', error);
    
    return new NextResponse('# Error generating metrics\n', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain; version=0.0.4; charset=utf-8',
      },
    });
  }
}

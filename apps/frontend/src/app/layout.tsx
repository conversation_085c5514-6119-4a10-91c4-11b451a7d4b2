import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "CivicPoll - Plateforme de Sondages SMATFLOW",
  description: "CivicPoll est la plateforme de sondages géolocalisés de SMATFLOW pour la France. Participez aux sondages de votre région et exprimez votre opinion.",
  keywords: ["sondages", "opinion", "france", "géolocalisation", "participation", "citoyenne"],
  authors: [{ name: "SMATFLOW" }],
  creator: "SMATFLOW",
  publisher: "SMATFLOW",
  robots: "index, follow",
  openGraph: {
    title: "CivicPoll - Plateforme de Sondages SMATFLOW",
    description: "Participez aux sondages géolocalisés de votre région avec CivicPoll",
    url: "https://civicpoll.fr.smatflow.xyz",
    siteName: "CivicPoll",
    locale: "fr_FR",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "CivicPoll - Plateforme de Sondages SMATFLOW",
    description: "Participez aux sondages géolocalisés de votre région",
  },
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#1f2937",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}

#!/bin/bash

# Array to store background process PIDs
declare -a pids

# Cleanup function to kill all background processes
cleanup() {
    echo "Cleaning up processes..."
    # Kill all processes in our pid array
    for pid in "${pids[@]}"; do
        if kill -0 $pid 2>/dev/null; then
            echo "Killing process $pid"
            kill $pid
        fi
    done
    exit 0
}

# Set up trap to catch SIGINT (Ctrl+C), SIGTERM, and EXIT
trap cleanup SIGINT SIGTERM EXIT

# Start the backend server in the background
cd apps/backend && pnpm build && pnpm start &
pids+=($!)  # Store the PID of the last background process

# Start the frontend server on PORT 3044
export PORT=3044
cd apps/frontend && pnpm build && pnpm start &
pids+=($!)  # Store the PID of the last background process

# Wait for all background processes
wait
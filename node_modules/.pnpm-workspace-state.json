{"lastValidatedTimestamp": 1750972324030, "projects": {"/home/<USER>/projects/smatflow/civicpoll": {"name": "civicpoll", "version": "0.1.0"}, "/home/<USER>/projects/smatflow/civicpoll/apps/backend": {"name": "backend", "version": "0.1.0"}, "/home/<USER>/projects/smatflow/civicpoll/apps/frontend": {"name": "frontend", "version": "0.1.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["apps/*", "packages/*"]}, "filteredInstall": false}
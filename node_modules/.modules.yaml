hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.27.7':
    '@babel/parser': private
  '@babel/runtime-corejs3@7.27.6':
    '@babel/runtime-corejs3': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.7':
    '@babel/traverse': private
  '@babel/types@7.27.7':
    '@babel/types': private
  '@casl/ability@6.5.0':
    '@casl/ability': private
  '@codemirror/autocomplete@6.18.6':
    '@codemirror/autocomplete': private
  '@codemirror/commands@6.8.1':
    '@codemirror/commands': private
  '@codemirror/lang-json@6.0.1':
    '@codemirror/lang-json': private
  '@codemirror/language@6.11.1':
    '@codemirror/language': private
  '@codemirror/lint@6.8.5':
    '@codemirror/lint': private
  '@codemirror/search@6.5.11':
    '@codemirror/search': private
  '@codemirror/state@6.5.2':
    '@codemirror/state': private
  '@codemirror/theme-one-dark@6.1.3':
    '@codemirror/theme-one-dark': private
  '@codemirror/view@6.37.2':
    '@codemirror/view': private
  '@colors/colors@1.5.0':
    '@colors/colors': private
  '@dabh/diagnostics@2.0.3':
    '@dabh/diagnostics': private
  '@discoveryjs/json-ext@0.5.7':
    '@discoveryjs/json-ext': private
  '@dnd-kit/accessibility@3.1.1(react@18.3.1)':
    '@dnd-kit/accessibility': private
  '@dnd-kit/core@6.3.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@dnd-kit/core': private
  '@dnd-kit/modifiers@9.0.0(@dnd-kit/core@6.3.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    '@dnd-kit/modifiers': private
  '@dnd-kit/sortable@10.0.0(@dnd-kit/core@6.3.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    '@dnd-kit/sortable': private
  '@dnd-kit/utilities@3.2.2(react@18.3.1)':
    '@dnd-kit/utilities': private
  '@emotion/babel-plugin@11.13.5':
    '@emotion/babel-plugin': private
  '@emotion/cache@11.14.0':
    '@emotion/cache': private
  '@emotion/hash@0.9.2':
    '@emotion/hash': private
  '@emotion/is-prop-valid@1.2.2':
    '@emotion/is-prop-valid': private
  '@emotion/memoize@0.8.1':
    '@emotion/memoize': private
  '@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1)':
    '@emotion/react': private
  '@emotion/serialize@1.3.3':
    '@emotion/serialize': private
  '@emotion/sheet@1.4.0':
    '@emotion/sheet': private
  '@emotion/unitless@0.8.1':
    '@emotion/unitless': private
  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@18.3.1)':
    '@emotion/use-insertion-effect-with-fallbacks': private
  '@emotion/utils@1.4.2':
    '@emotion/utils': private
  '@emotion/weak-memoize@0.4.0':
    '@emotion/weak-memoize': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@floating-ui/core@1.7.1':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.1':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@formatjs/ecma402-abstract@1.18.2':
    '@formatjs/ecma402-abstract': private
  '@formatjs/fast-memoize@2.2.0':
    '@formatjs/fast-memoize': private
  '@formatjs/icu-messageformat-parser@2.7.6':
    '@formatjs/icu-messageformat-parser': private
  '@formatjs/icu-skeleton-parser@1.8.0':
    '@formatjs/icu-skeleton-parser': private
  '@formatjs/intl-displaynames@6.6.6':
    '@formatjs/intl-displaynames': private
  '@formatjs/intl-listformat@7.5.5':
    '@formatjs/intl-listformat': private
  '@formatjs/intl-localematcher@0.5.4':
    '@formatjs/intl-localematcher': private
  '@formatjs/intl@2.10.0(typescript@5.8.3)':
    '@formatjs/intl': private
  '@hapi/bourne@3.0.0':
    '@hapi/bourne': private
  '@img/sharp-darwin-arm64@0.34.2':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.2':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.2':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.2':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.2':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.2':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.2':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.2':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.2':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.2':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.2':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.2':
    '@img/sharp-win32-x64': private
  '@inquirer/figures@1.0.12':
    '@inquirer/figures': private
  '@internationalized/date@3.5.4':
    '@internationalized/date': private
  '@internationalized/number@3.5.3':
    '@internationalized/number': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@juggle/resize-observer@3.4.0':
    '@juggle/resize-observer': private
  '@koa/cors@5.0.0':
    '@koa/cors': private
  '@koa/router@12.0.2':
    '@koa/router': private
  '@lezer/common@1.2.3':
    '@lezer/common': private
  '@lezer/highlight@1.2.1':
    '@lezer/highlight': private
  '@lezer/json@1.0.3':
    '@lezer/json': private
  '@lezer/lr@1.4.2':
    '@lezer/lr': private
  '@marijn/find-cluster-break@1.0.2':
    '@marijn/find-cluster-break': private
  '@mux/mux-player-react@3.1.0(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@mux/mux-player-react': private
  '@mux/mux-player@3.1.0':
    '@mux/mux-player': private
  '@mux/mux-video@0.22.0':
    '@mux/mux-video': private
  '@mux/playback-core@0.27.0':
    '@mux/playback-core': private
  '@next/env@15.3.4':
    '@next/env': private
  '@next/swc-darwin-arm64@15.3.4':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.3.4':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.3.4':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.3.4':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.3.4':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.3.4':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.3.4':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.3.4':
    '@next/swc-win32-x64-msvc': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@paralleldrive/cuid2@2.2.2':
    '@paralleldrive/cuid2': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pmmmwh/react-refresh-webpack-plugin@0.5.15(react-refresh@0.14.0)(type-fest@4.41.0)(webpack-hot-middleware@2.26.1)(webpack@5.99.9(esbuild@0.25.5))':
    '@pmmmwh/react-refresh-webpack-plugin': private
  '@pnpm/config.env-replace@1.1.0':
    '@pnpm/config.env-replace': private
  '@pnpm/network.ca-file@1.0.2':
    '@pnpm/network.ca-file': private
  '@pnpm/npm-conf@2.3.1':
    '@pnpm/npm-conf': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@radix-ui/number@1.0.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.0.1':
    '@radix-ui/primitive': private
  '@radix-ui/react-accordion@1.1.2(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-accordion': private
  '@radix-ui/react-alert-dialog@1.0.5(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-alert-dialog': private
  '@radix-ui/react-arrow@1.0.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-avatar@1.0.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-avatar': private
  '@radix-ui/react-checkbox@1.0.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-checkbox': private
  '@radix-ui/react-collapsible@1.0.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collapsible': private
  '@radix-ui/react-collection@1.0.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-dialog@1.0.5(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dialog': private
  '@radix-ui/react-direction@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.0.5(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-dropdown-menu@2.0.6(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dropdown-menu': private
  '@radix-ui/react-focus-guards@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.0.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.0.6(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popover@1.0.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popover': private
  '@radix-ui/react-popper@1.1.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.0.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.0.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@1.0.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-progress@1.0.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-progress': private
  '@radix-ui/react-radio-group@1.1.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-radio-group': private
  '@radix-ui/react-roving-focus@1.0.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-scroll-area@1.0.5(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-scroll-area': private
  '@radix-ui/react-separator@1.0.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-separator': private
  '@radix-ui/react-slot@1.0.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-slot': private
  '@radix-ui/react-switch@1.0.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-switch': private
  '@radix-ui/react-tabs@1.0.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-tabs': private
  '@radix-ui/react-toggle-group@1.0.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-toggle-group': private
  '@radix-ui/react-toggle@1.0.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-toggle': private
  '@radix-ui/react-toolbar@1.0.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-toolbar': private
  '@radix-ui/react-tooltip@1.0.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-tooltip': private
  '@radix-ui/react-use-callback-ref@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-escape-keydown@1.0.3(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.0.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.0.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.0.1':
    '@radix-ui/rect': private
  '@react-dnd/asap@5.0.2':
    '@react-dnd/asap': private
  '@react-dnd/invariant@4.0.2':
    '@react-dnd/invariant': private
  '@react-dnd/shallowequal@4.0.2':
    '@react-dnd/shallowequal': private
  '@reduxjs/toolkit@1.9.7(react-redux@8.1.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(redux@4.2.1))(react@18.3.1)':
    '@reduxjs/toolkit': private
  '@remix-run/router@1.23.0':
    '@remix-run/router': private
  '@rollup/rollup-android-arm-eabi@4.44.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@rushstack/node-core-library@5.13.0(@types/node@20.19.1)':
    '@rushstack/node-core-library': private
  '@rushstack/terminal@0.15.2(@types/node@20.19.1)':
    '@rushstack/terminal': private
  '@rushstack/ts-command-line@4.23.7(@types/node@20.19.1)':
    '@rushstack/ts-command-line': private
  '@simov/deep-extend@1.0.0':
    '@simov/deep-extend': private
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': private
  '@sindresorhus/slugify@1.1.0':
    '@sindresorhus/slugify': private
  '@sindresorhus/transliterate@0.1.2':
    '@sindresorhus/transliterate': private
  '@strapi/admin@5.16.1(@babel/runtime@7.27.6)(@codemirror/autocomplete@6.18.6)(@codemirror/language@6.11.1)(@codemirror/lint@6.8.5)(@codemirror/search@6.5.11)(@codemirror/state@6.5.2)(@codemirror/theme-one-dark@6.1.3)(@codemirror/view@6.37.2)(@strapi/data-transfer@5.16.1(@types/node@20.19.1)(better-sqlite3@11.3.0)(typescript@5.4.4))(@types/hoist-non-react-statics@3.3.6)(@types/node@20.19.1)(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(better-sqlite3@11.3.0)(codemirror@5.65.19)(debug@4.3.4)(react-dom@18.3.1(react@18.3.1))(react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)(redux@4.2.1)(styled-components@6.1.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1))':
    '@strapi/admin': private
  '@strapi/cloud-cli@5.16.1':
    '@strapi/cloud-cli': private
  '@strapi/content-manager@5.16.1(424060acecff9d1121ec0ad0ac2871ba)':
    '@strapi/content-manager': private
  '@strapi/content-releases@5.16.1(a193d91a5e2da3d8acd6639036a5c813)':
    '@strapi/content-releases': private
  '@strapi/content-type-builder@5.16.1(3c90655e321a891770a3bdde4f88e55b)':
    '@strapi/content-type-builder': private
  '@strapi/core@5.16.1(@babel/runtime@7.27.6)(@codemirror/autocomplete@6.18.6)(@codemirror/language@6.11.1)(@codemirror/lint@6.8.5)(@codemirror/search@6.5.11)(@codemirror/state@6.5.2)(@codemirror/theme-one-dark@6.1.3)(@codemirror/view@6.37.2)(@strapi/data-transfer@5.16.1(@types/node@20.19.1)(better-sqlite3@11.3.0)(typescript@5.4.4))(@types/hoist-non-react-statics@3.3.6)(@types/node@20.19.1)(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(better-sqlite3@11.3.0)(codemirror@5.65.19)(react-dom@18.3.1(react@18.3.1))(react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)(redux@4.2.1)(styled-components@6.1.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1))':
    '@strapi/core': private
  '@strapi/data-transfer@5.16.1(@types/node@20.19.1)(better-sqlite3@11.3.0)(typescript@5.8.3)':
    '@strapi/data-transfer': private
  '@strapi/database@5.16.1(@types/node@20.19.1)(better-sqlite3@11.3.0)':
    '@strapi/database': private
  '@strapi/design-system@2.0.0-rc.27(@babel/runtime@7.27.6)(@codemirror/autocomplete@6.18.6)(@codemirror/language@6.11.1)(@codemirror/lint@6.8.5)(@codemirror/search@6.5.11)(@codemirror/state@6.5.2)(@codemirror/theme-one-dark@6.1.3)(@codemirror/view@6.37.2)(@strapi/icons@2.0.0-rc.27(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(styled-components@6.1.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1)))(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(codemirror@5.65.19)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(styled-components@6.1.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1))':
    '@strapi/design-system': private
  '@strapi/email@5.16.1(07b3f0d7a57cfe440c36ef345d864ce7)':
    '@strapi/email': private
  '@strapi/generators@5.16.1':
    '@strapi/generators': private
  '@strapi/i18n@5.16.1(e4c9110756e8ad4c076d9e6001d5a701)':
    '@strapi/i18n': private
  '@strapi/icons@2.0.0-rc.27(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(styled-components@6.1.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1))':
    '@strapi/icons': private
  '@strapi/logger@5.16.1':
    '@strapi/logger': private
  '@strapi/permissions@5.16.1':
    '@strapi/permissions': private
  '@strapi/plugin-cloud@5.16.1(e01ba2417b0c5766d87e1370d9e66c65)':
    '@strapi/plugin-cloud': private
  '@strapi/plugin-users-permissions@5.16.1(d510f3eafea211a939bf4fbc937d4271)':
    '@strapi/plugin-users-permissions': private
  '@strapi/provider-email-sendmail@5.16.1':
    '@strapi/provider-email-sendmail': private
  '@strapi/provider-upload-local@5.16.1':
    '@strapi/provider-upload-local': private
  '@strapi/review-workflows@5.16.1(da4505499be1f8482655c4316bdb707f)':
    '@strapi/review-workflows': private
  '@strapi/strapi@5.16.1(@babel/runtime@7.27.6)(@codemirror/autocomplete@6.18.6)(@codemirror/language@6.11.1)(@codemirror/lint@6.8.5)(@codemirror/search@6.5.11)(@codemirror/state@6.5.2)(@codemirror/theme-one-dark@6.1.3)(@codemirror/view@6.37.2)(@types/hoist-non-react-statics@3.3.6)(@types/node@20.19.1)(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(better-sqlite3@11.3.0)(codemirror@5.65.19)(esbuild@0.25.5)(koa@2.16.1)(lightningcss@1.30.1)(react-dom@18.3.1(react@18.3.1))(react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)(redux@4.2.1)(styled-components@6.1.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(terser@5.43.1)(type-fest@4.41.0)':
    '@strapi/strapi': private
  '@strapi/types@5.16.1(@types/node@20.19.1)(better-sqlite3@11.3.0)(typescript@5.4.4)':
    '@strapi/types': private
  '@strapi/typescript-utils@5.16.1':
    '@strapi/typescript-utils': private
  '@strapi/ui-primitives@2.0.0-rc.27(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@strapi/ui-primitives': private
  '@strapi/upload@5.16.1(aa69231b099c8978c01f13210be4bd62)':
    '@strapi/upload': private
  '@strapi/utils@5.16.1':
    '@strapi/utils': private
  '@swc/core-darwin-arm64@1.12.7':
    '@swc/core-darwin-arm64': private
  '@swc/core-darwin-x64@1.12.7':
    '@swc/core-darwin-x64': private
  '@swc/core-linux-arm-gnueabihf@1.12.7':
    '@swc/core-linux-arm-gnueabihf': private
  '@swc/core-linux-arm64-gnu@1.12.7':
    '@swc/core-linux-arm64-gnu': private
  '@swc/core-linux-arm64-musl@1.12.7':
    '@swc/core-linux-arm64-musl': private
  '@swc/core-linux-x64-gnu@1.12.7':
    '@swc/core-linux-x64-gnu': private
  '@swc/core-linux-x64-musl@1.12.7':
    '@swc/core-linux-x64-musl': private
  '@swc/core-win32-arm64-msvc@1.12.7':
    '@swc/core-win32-arm64-msvc': private
  '@swc/core-win32-ia32-msvc@1.12.7':
    '@swc/core-win32-ia32-msvc': private
  '@swc/core-win32-x64-msvc@1.12.7':
    '@swc/core-win32-x64-msvc': private
  '@swc/core@1.12.7':
    '@swc/core': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@swc/types@0.1.23':
    '@swc/types': private
  '@szmarczak/http-timer@4.0.6':
    '@szmarczak/http-timer': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.11':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.11':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@tailwindcss/postcss@4.1.11':
    '@tailwindcss/postcss': private
  '@testing-library/dom@10.1.0':
    '@testing-library/dom': private
  '@testing-library/react@15.0.7(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@testing-library/react': private
  '@testing-library/user-event@14.5.2(@testing-library/dom@10.1.0)':
    '@testing-library/user-event': private
  '@types/accepts@1.3.7':
    '@types/accepts': private
  '@types/argparse@1.0.38':
    '@types/argparse': private
  '@types/aria-query@5.0.4':
    '@types/aria-query': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/cacheable-request@6.0.3':
    '@types/cacheable-request': private
  '@types/co-body@6.1.3':
    '@types/co-body': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/content-disposition@0.5.9':
    '@types/content-disposition': private
  '@types/cookies@0.9.1':
    '@types/cookies': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': private
  '@types/express@4.17.23':
    '@types/express': private
  '@types/fined@1.1.5':
    '@types/fined': private
  '@types/follow-redirects@1.14.4':
    '@types/follow-redirects': private
  '@types/formidable@2.0.6':
    '@types/formidable': private
  '@types/glob@7.2.0':
    '@types/glob': private
  '@types/hoist-non-react-statics@3.3.6':
    '@types/hoist-non-react-statics': private
  '@types/html-minifier-terser@6.1.0':
    '@types/html-minifier-terser': private
  '@types/http-assert@1.5.6':
    '@types/http-assert': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/inquirer@6.5.0':
    '@types/inquirer': private
  '@types/is-hotkey@0.1.10':
    '@types/is-hotkey': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/jsonwebtoken@9.0.10':
    '@types/jsonwebtoken': private
  '@types/keygrip@1.0.6':
    '@types/keygrip': private
  '@types/keyv@3.1.4':
    '@types/keyv': private
  '@types/koa-compose@3.2.8':
    '@types/koa-compose': private
  '@types/koa@2.15.0':
    '@types/koa': private
  '@types/liftoff@4.0.3':
    '@types/liftoff': private
  '@types/lodash@4.17.19':
    '@types/lodash': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/minimatch@5.1.2':
    '@types/minimatch': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node@20.19.1':
    '@types/node': private
  '@types/nodemon@1.19.6':
    '@types/nodemon': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/react-dom@18.3.7(@types/react@18.3.23)':
    '@types/react-dom': private
  '@types/react-transition-group@4.4.12(@types/react@18.3.23)':
    '@types/react-transition-group': private
  '@types/react@18.3.23':
    '@types/react': private
  '@types/responselike@1.0.3':
    '@types/responselike': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/stylis@4.2.5':
    '@types/stylis': private
  '@types/through@0.0.33':
    '@types/through': private
  '@types/triple-beam@1.3.5':
    '@types/triple-beam': private
  '@types/use-sync-external-store@0.0.3':
    '@types/use-sync-external-store': private
  '@ucast/core@1.10.2':
    '@ucast/core': private
  '@ucast/js@3.0.4':
    '@ucast/js': private
  '@ucast/mongo2js@1.4.0':
    '@ucast/mongo2js': private
  '@ucast/mongo@2.4.3':
    '@ucast/mongo': private
  '@uiw/codemirror-extensions-basic-setup@4.22.2(@codemirror/autocomplete@6.18.6)(@codemirror/commands@6.8.1)(@codemirror/language@6.11.1)(@codemirror/lint@6.8.5)(@codemirror/search@6.5.11)(@codemirror/state@6.5.2)(@codemirror/view@6.37.2)':
    '@uiw/codemirror-extensions-basic-setup': private
  '@uiw/react-codemirror@4.22.2(@babel/runtime@7.27.6)(@codemirror/autocomplete@6.18.6)(@codemirror/language@6.11.1)(@codemirror/lint@6.8.5)(@codemirror/search@6.5.11)(@codemirror/state@6.5.2)(@codemirror/theme-one-dark@6.1.3)(@codemirror/view@6.37.2)(codemirror@5.65.19)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@uiw/react-codemirror': private
  '@vitejs/plugin-react-swc@3.6.0(vite@5.4.19(@types/node@20.19.1)(lightningcss@1.30.1)(terser@5.43.1))':
    '@vitejs/plugin-react-swc': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  accepts@1.3.8:
    accepts: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  addressparser@1.0.1:
    addressparser: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv-draft-04@1.0.0(ajv@8.13.0):
    ajv-draft-04: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@8.16.0:
    ajv: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-html-community@0.0.8:
    ansi-html-community: private
  ansi-html@0.0.9:
    ansi-html: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-sequence-parser@1.1.3:
    ansi-sequence-parser: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  apps/backend:
    backend: private
  apps/frontend:
    frontend: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  aria-query@5.3.0:
    aria-query: private
  array-each@1.0.1:
    array-each: private
  array-slice@1.1.0:
    array-slice: private
  array-union@2.1.0:
    array-union: private
  asap@2.0.6:
    asap: private
  asn1.js@5.4.1:
    asn1.js: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  axios@1.8.4(debug@4.3.4):
    axios: private
  babel-plugin-macros@3.1.0:
    babel-plugin-macros: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  bcryptjs@2.4.3:
    bcryptjs: private
  better-sqlite3@11.3.0:
    better-sqlite3: private
  big-integer@1.6.52:
    big-integer: private
  big.js@5.2.2:
    big.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bindings@1.5.0:
    bindings: private
  bl@4.1.0:
    bl: private
  bn.js@4.12.2:
    bn.js: private
  boolbase@1.0.0:
    boolbase: private
  boolean@3.2.0:
    boolean: private
  boxen@5.1.2:
    boxen: private
  brace-expansion@2.0.2:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  broadcast-channel@3.7.0:
    broadcast-channel: private
  brorand@1.1.0:
    brorand: private
  browserslist-to-esbuild@1.2.0:
    browserslist-to-esbuild: private
  browserslist@4.25.1:
    browserslist: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@5.7.1:
    buffer: private
  buildmail@3.10.0:
    buildmail: private
  busboy@1.6.0:
    busboy: private
  byte-size@8.1.1:
    byte-size: private
  bytes@3.1.2:
    bytes: private
  cache-content-type@1.0.1:
    cache-content-type: private
  cacheable-lookup@5.0.4:
    cacheable-lookup: private
  cacheable-request@7.0.4:
    cacheable-request: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@4.1.2:
    camel-case: private
  camelcase@6.3.0:
    camelcase: private
  camelize@1.0.1:
    camelize: private
  caniuse-lite@1.0.30001726:
    caniuse-lite: private
  capital-case@1.0.4:
    capital-case: private
  castable-video@1.1.10:
    castable-video: private
  chalk@4.1.2:
    chalk: private
  change-case@3.1.0:
    change-case: private
  chardet@0.7.0:
    chardet: private
  chokidar@3.6.0:
    chokidar: private
  chownr@1.1.4:
    chownr: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  ci-info@3.8.0:
    ci-info: private
  clean-css@5.3.3:
    clean-css: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-boxes@2.2.1:
    cli-boxes: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-progress@3.12.0:
    cli-progress: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-table3@0.6.5:
    cli-table3: private
  cli-width@3.0.0:
    cli-width: private
  client-only@0.0.1:
    client-only: private
  cliui@7.0.4:
    cliui: private
  clone-response@1.0.3:
    clone-response: private
  clone@1.0.4:
    clone: private
  co-body@6.2.0:
    co-body: private
  co@4.6.0:
    co: private
  codemirror@5.65.19:
    codemirror: private
    codemirror5: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  colorette@2.0.20:
    colorette: private
  colorspace@1.1.4:
    colorspace: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@8.3.0:
    commander: private
  compressible@2.0.18:
    compressible: private
  compute-scroll-into-view@1.0.20:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  concurrently@8.2.2:
    concurrently: private
  config-chain@1.1.13:
    config-chain: private
  configstore@5.0.1:
    configstore: private
  constant-case@2.0.0:
    constant-case: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@1.9.0:
    convert-source-map: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  cookies@0.9.1:
    cookies: private
  copyfiles@2.4.1:
    copyfiles: private
  core-js-pure@3.43.0:
    core-js-pure: private
  core-util-is@1.0.3:
    core-util-is: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  crc@3.8.0:
    crc: private
  crelt@1.0.6:
    crelt: private
  cron-parser@4.9.0:
    cron-parser: private
  cropperjs@1.6.1:
    cropperjs: private
  cross-env@7.0.3:
    cross-env: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  css-color-keywords@1.0.0:
    css-color-keywords: private
  css-loader@6.11.0(webpack@5.99.9(esbuild@0.25.5)):
    css-loader: private
  css-select@4.3.0:
    css-select: private
  css-to-react-native@3.2.0:
    css-to-react-native: private
  css-what@6.1.0:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  custom-media-element@1.3.3:
    custom-media-element: private
  date-fns-tz@2.0.1(date-fns@2.30.0):
    date-fns-tz: private
  date-fns@2.30.0:
    date-fns: private
  debounce@1.2.1:
    debounce: private
  debug@4.3.4:
    debug: private
  decompress-response@7.0.0:
    decompress-response: private
  deep-equal@1.0.1:
    deep-equal: private
  deep-extend@0.6.0:
    deep-extend: private
  deepmerge@4.3.1:
    deepmerge: private
  defaults@1.0.4:
    defaults: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  del@5.1.0:
    del: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  destroy@1.2.0:
    destroy: private
  detect-file@1.0.0:
    detect-file: private
  detect-indent@6.1.0:
    detect-indent: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  detect-node@2.1.0:
    detect-node: private
  dezalgo@1.0.4:
    dezalgo: private
  dir-glob@3.0.1:
    dir-glob: private
  direction@1.0.4:
    direction: private
  dkim-signer@0.2.2:
    dkim-signer: private
  dnd-core@16.0.1:
    dnd-core: private
  dom-accessibility-api@0.5.16:
    dom-accessibility-api: private
  dom-converter@0.2.0:
    dom-converter: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dot-case@2.1.1:
    dot-case: private
  dot-prop@5.3.0:
    dot-prop: private
  dotenv@16.4.5:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.176:
    electron-to-chromium: private
  elliptic@6.6.1:
    elliptic: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  enabled@2.0.0:
    enabled: private
  encodeurl@1.0.2:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  entities@3.0.1:
    entities: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser@2.1.4:
    error-stack-parser: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es6-error@4.1.1:
    es6-error: private
  esbuild-loader@4.3.0(webpack@5.99.9(esbuild@0.25.5)):
    esbuild-loader: private
  esbuild-register@3.5.0(esbuild@0.25.5):
    esbuild-register: private
  esbuild@0.25.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@5.1.1:
    eslint-scope: private
  esm@3.2.25:
    esm: private
  esprima@4.0.1:
    esprima: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  events@3.3.0:
    events: private
  eventsource@2.0.2:
    eventsource: private
  execa@5.1.1:
    execa: private
  expand-template@2.0.3:
    expand-template: private
  expand-tilde@2.0.2:
    expand-tilde: private
  extend@3.0.2:
    extend: private
  external-editor@3.1.0:
    external-editor: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fecha@4.2.3:
    fecha: private
  figures@3.2.0:
    figures: private
  file-uri-to-path@1.0.0:
    file-uri-to-path: private
  fill-range@7.1.1:
    fill-range: private
  find-root@1.1.0:
    find-root: private
  find-up@3.0.0:
    find-up: private
  find-yarn-workspace-root2@1.2.16:
    find-yarn-workspace-root2: private
  findup-sync@5.0.0:
    findup-sync: private
  fined@2.0.0:
    fined: private
  flagged-respawn@2.0.0:
    flagged-respawn: private
  fn.name@1.1.0:
    fn.name: private
  follow-redirects@1.15.9(debug@4.3.4):
    follow-redirects: private
  for-in@1.0.2:
    for-in: private
  for-own@1.0.0:
    for-own: private
  foreground-child@3.3.1:
    foreground-child: private
  fork-ts-checker-webpack-plugin@8.0.0(typescript@5.4.4)(webpack@5.99.9(esbuild@0.25.5)):
    fork-ts-checker-webpack-plugin: private
  form-data@4.0.3:
    form-data: private
  formidable@2.1.5:
    formidable: private
  formik@2.4.5(react@18.3.1):
    formik: private
  fractional-indexing@3.2.0:
    fractional-indexing: private
  fresh@0.5.2:
    fresh: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@11.2.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs-monkey@1.0.6:
    fs-monkey: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-it@8.6.9:
    get-it: private
  get-latest-version@5.1.0:
    get-latest-version: private
  get-nonce@1.0.1:
    get-nonce: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  getopts@2.3.0:
    getopts: private
  git-up@7.0.0:
    git-up: private
  git-url-parse@14.0.0:
    git-url-parse: private
  github-from-package@0.0.0:
    github-from-package: private
  glob-parent@5.1.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@10.3.10:
    glob: private
  global-agent@3.0.0:
    global-agent: private
  global-modules@1.0.0:
    global-modules: private
  global-prefix@1.0.2:
    global-prefix: private
  globals@11.12.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@10.0.2:
    globby: private
  gopd@1.2.0:
    gopd: private
  got@11.8.6:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  grant@5.4.24:
    grant: private
  gzip-size@6.0.0:
    gzip-size: private
  handlebars@4.7.8:
    handlebars: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hash.js@1.1.7:
    hash.js: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  header-case@1.0.1:
    header-case: private
  helmet@6.2.0:
    helmet: private
  highlight.js@10.7.3:
    highlight.js: private
  hls.js@1.5.20:
    hls.js: private
  hmac-drbg@1.0.1:
    hmac-drbg: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  homedir-polyfill@1.0.3:
    homedir-polyfill: private
  hosted-git-info@2.8.9:
    hosted-git-info: private
  html-entities@2.6.0:
    html-entities: private
  html-escaper@2.0.2:
    html-escaper: private
  html-minifier-terser@6.1.0:
    html-minifier-terser: private
  html-webpack-plugin@5.6.0(webpack@5.99.9(esbuild@0.25.5)):
    html-webpack-plugin: private
  htmlparser2@8.0.2:
    htmlparser2: private
  http-assert@1.5.0:
    http-assert: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http-errors@2.0.0:
    http-errors: private
  http2-wrapper@1.0.3:
    http2-wrapper: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  icss-utils@5.1.0(postcss@8.5.6):
    icss-utils: private
  ieee754@1.2.1:
    ieee754: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  ignore-walk@3.0.4:
    ignore-walk: private
  ignore@5.3.2:
    ignore: private
  immer@9.0.21:
    immer: private
  import-fresh@3.3.1:
    import-fresh: private
  import-lazy@4.0.0:
    import-lazy: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflation@2.1.0:
    inflation: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@2.0.0:
    ini: private
  inquirer@8.2.5:
    inquirer: private
  interpret@2.2.0:
    interpret: private
  intl-messageformat@10.5.11:
    intl-messageformat: private
  invariant@2.2.4:
    invariant: private
  is-absolute@1.0.0:
    is-absolute: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-class-hotfix@0.0.6:
    is-class-hotfix: private
  is-core-module@2.16.1:
    is-core-module: private
  is-docker@2.2.1:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hotkey@0.1.8:
    is-hotkey: private
  is-interactive@1.0.0:
    is-interactive: private
  is-localhost-ip@2.0.0:
    is-localhost-ip: private
  is-lower-case@1.1.3:
    is-lower-case: private
  is-number@7.0.0:
    is-number: private
  is-obj@2.0.0:
    is-obj: private
  is-path-cwd@2.2.0:
    is-path-cwd: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-regex@1.2.1:
    is-regex: private
  is-relative@1.0.0:
    is-relative: private
  is-retry-allowed@2.2.0:
    is-retry-allowed: private
  is-ssh@1.4.1:
    is-ssh: private
  is-stream@2.0.1:
    is-stream: private
  is-type-of@1.4.0:
    is-type-of: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-unc-path@1.0.0:
    is-unc-path: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-upper-case@1.1.2:
    is-upper-case: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@2.2.0:
    is-wsl: private
  isarray@0.0.1:
    isarray: private
  isbinaryfile@4.0.10:
    isbinaryfile: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  isstream@0.1.2:
    isstream: private
  jackspeak@2.3.6:
    jackspeak: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@2.4.2:
    jiti: private
  jju@1.4.0:
    jju: private
  jose@4.15.9:
    jose: private
  js-sha3@0.8.0:
    js-sha3: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@3.14.1:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-logic-js@2.0.5:
    json-logic-js: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json5@2.2.3:
    json5: private
  jsonc-parser@3.3.1:
    jsonc-parser: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonwebtoken@9.0.0:
    jsonwebtoken: private
  jwa@1.4.2:
    jwa: private
  jwk-to-pem@2.0.5:
    jwk-to-pem: private
  jwks-rsa@3.1.0:
    jwks-rsa: private
  jws@4.0.0:
    jws: private
  keygrip@1.1.0:
    keygrip: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  knex@3.0.1(better-sqlite3@11.3.0):
    knex: private
  koa-body@6.0.1:
    koa-body: private
  koa-compose@4.1.0:
    koa-compose: private
  koa-compress@5.1.1:
    koa-compress: private
  koa-convert@2.0.0:
    koa-convert: private
  koa-favicon@2.1.0:
    koa-favicon: private
  koa-helmet@7.0.2:
    koa-helmet: private
  koa-ip@2.1.3:
    koa-ip: private
  koa-is-json@1.0.0:
    koa-is-json: private
  koa-passport@6.0.0:
    koa-passport: private
  koa-range@0.3.0:
    koa-range: private
  koa-send@5.0.1:
    koa-send: private
  koa-session@6.4.0:
    koa-session: private
  koa-static@5.0.0:
    koa-static: private
  koa2-ratelimit@1.1.3:
    koa2-ratelimit: private
  koa@2.16.1:
    koa: private
  kuler@2.0.0:
    kuler: private
  libbase64@0.1.0:
    libbase64: private
  libmime@2.1.3:
    libmime: private
  libqp@1.1.0:
    libqp: private
  liftoff@4.0.0:
    liftoff: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  limiter@1.1.5:
    limiter: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  linkify-it@4.0.1:
    linkify-it: private
  load-yaml-file@0.2.0:
    load-yaml-file: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@2.0.4:
    loader-utils: private
  locate-path@3.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.clonedeep@4.5.0:
    lodash.clonedeep: private
  lodash.deburr@4.1.0:
    lodash.deburr: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  logform@2.7.0:
    logform: private
  long-timeout@0.1.1:
    long-timeout: private
  loose-envify@1.4.0:
    loose-envify: private
  lower-case-first@1.0.2:
    lower-case-first: private
  lower-case@1.1.4:
    lower-case: private
  lowercase-keys@2.0.0:
    lowercase-keys: private
  lru-cache@6.0.0:
    lru-cache: private
  lru-memoizer@2.3.0:
    lru-memoizer: private
  lunr@2.3.9:
    lunr: private
  luxon@3.6.1:
    luxon: private
  lz-string@1.5.0:
    lz-string: private
  magic-string@0.30.17:
    magic-string: private
  mailcomposer@3.12.0:
    mailcomposer: private
  make-dir@3.1.0:
    make-dir: private
  make-iterator@1.0.1:
    make-iterator: private
  map-cache@0.2.2:
    map-cache: private
  markdown-it-abbr@1.0.4:
    markdown-it-abbr: private
  markdown-it-container@3.0.0:
    markdown-it-container: private
  markdown-it-deflist@2.1.0:
    markdown-it-deflist: private
  markdown-it-emoji@2.0.2:
    markdown-it-emoji: private
  markdown-it-footnote@3.0.3:
    markdown-it-footnote: private
  markdown-it-ins@3.0.1:
    markdown-it-ins: private
  markdown-it-mark@3.0.1:
    markdown-it-mark: private
  markdown-it-sub@1.0.0:
    markdown-it-sub: private
  markdown-it-sup@1.0.0:
    markdown-it-sup: private
  markdown-it@13.0.2:
    markdown-it: private
  marked@4.3.0:
    marked: private
  match-sorter@6.3.4:
    match-sorter: private
  matcher@3.0.0:
    matcher: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdurl@1.0.1:
    mdurl: private
  media-chrome@4.2.3:
    media-chrome: private
  media-tracks@0.3.3:
    media-tracks: private
  media-typer@0.3.0:
    media-typer: private
  memfs@3.5.3:
    memfs: private
  memoize-one@6.0.0:
    memoize-one: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  microseconds@0.2.0:
    microseconds: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  mimic-response@3.1.0:
    mimic-response: private
  mini-css-extract-plugin@2.7.7(webpack@5.99.9(esbuild@0.25.5)):
    mini-css-extract-plugin: private
  minimalistic-assert@1.0.1:
    minimalistic-assert: private
  minimalistic-crypto-utils@1.0.1:
    minimalistic-crypto-utils: private
  minimatch@9.0.3:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  mkdirp@1.0.4:
    mkdirp: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  multistream@4.1.0:
    multistream: private
  mute-stream@0.0.8:
    mute-stream: private
  mux-embed@5.9.0:
    mux-embed: private
  mz@2.7.0:
    mz: private
  nano-time@1.0.0:
    nano-time: private
  nanoclone@0.2.1:
    nanoclone: private
  nanoid@3.3.11:
    nanoid: private
  napi-build-utils@2.0.0:
    napi-build-utils: private
  negotiator@0.6.3:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  next@15.3.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    next: private
  no-case@2.3.2:
    no-case: private
  node-abi@3.75.0:
    node-abi: private
  node-abort-controller@3.1.1:
    node-abort-controller: private
  node-machine-id@1.1.12:
    node-machine-id: private
  node-plop@0.26.3:
    node-plop: private
  node-releases@2.0.19:
    node-releases: private
  node-schedule@2.1.1:
    node-schedule: private
  nodemailer-fetch@1.6.0:
    nodemailer-fetch: private
  nodemailer-shared@1.1.0:
    nodemailer-shared: private
  nodemon@3.0.2:
    nodemon: private
  noms@0.0.0:
    noms: private
  normalize-package-data@2.5.0:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-url@6.1.0:
    normalize-url: private
  npm-bundled@1.1.2:
    npm-bundled: private
  npm-normalize-package-bin@1.0.1:
    npm-normalize-package-bin: private
  npm-packlist@2.2.2:
    npm-packlist: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  oauth-sign@0.9.0:
    oauth-sign: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.defaults@1.1.0:
    object.defaults: private
  object.map@1.0.1:
    object.map: private
  object.pick@1.3.0:
    object.pick: private
  oblivious-set@1.0.0:
    oblivious-set: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  one-time@1.0.0:
    one-time: private
  onetime@5.1.2:
    onetime: private
  only@0.0.2:
    only: private
  open@8.4.0:
    open: private
  opener@1.5.2:
    opener: private
  ora@5.4.1:
    ora: private
  os-paths@7.4.0:
    os-paths: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  outdent@0.8.0:
    outdent: private
  p-cancelable@2.1.1:
    p-cancelable: private
  p-limit@2.3.0:
    p-limit: private
  p-locate@3.0.0:
    p-locate: private
  p-map@4.0.0:
    p-map: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-json@7.0.0:
    package-json: private
  param-case@3.0.4:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-filepath@1.0.2:
    parse-filepath: private
  parse-json@5.2.0:
    parse-json: private
  parse-passwd@1.0.0:
    parse-passwd: private
  parse-path@7.1.0:
    parse-path: private
  parse-srcset@1.0.2:
    parse-srcset: private
  parse-url@8.1.0:
    parse-url: private
  parseurl@1.3.3:
    parseurl: private
  pascal-case@3.1.2:
    pascal-case: private
  passport-local@1.0.0:
    passport-local: private
  passport-strategy@1.0.0:
    passport-strategy: private
  passport@0.6.0:
    passport: private
  path-case@2.1.1:
    path-case: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-root-regex@0.1.2:
    path-root-regex: private
  path-root@0.1.1:
    path-root: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pause@0.0.1:
    pause: private
  pg-connection-string@2.6.1:
    pg-connection-string: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@4.0.1:
    pify: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pkg-up@3.1.0:
    pkg-up: private
  player.style@0.0.8:
    player.style: private
  plop@4.0.1:
    plop: private
  pluralize@8.0.0:
    pluralize: private
  pony-cause@2.1.11:
    pony-cause: private
  postcss-modules-extract-imports@3.1.0(postcss@8.5.6):
    postcss-modules-extract-imports: private
  postcss-modules-local-by-default@4.2.0(postcss@8.5.6):
    postcss-modules-local-by-default: private
  postcss-modules-scope@3.2.1(postcss@8.5.6):
    postcss-modules-scope: private
  postcss-modules-values@4.0.0(postcss@8.5.6):
    postcss-modules-values: private
  postcss-selector-parser@7.1.0:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  prebuild-install@7.1.3:
    prebuild-install: private
  preferred-pm@3.1.2:
    preferred-pm: private
  pretty-error@4.0.0:
    pretty-error: private
  pretty-format@27.5.1:
    pretty-format: private
  prismjs@1.30.0:
    prismjs: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  prop-types@15.8.1:
    prop-types: private
  property-expr@2.0.6:
    property-expr: private
  proto-list@1.2.4:
    proto-list: private
  protocols@2.0.2:
    protocols: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  pstree.remy@1.1.8:
    pstree.remy: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  purest@4.0.2:
    purest: private
  qs@6.11.1:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-lru@5.1.1:
    quick-lru: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  rc@1.2.8:
    rc: private
  react-dnd-html5-backend@16.0.1:
    react-dnd-html5-backend: private
  react-dnd@16.0.1(@types/hoist-non-react-statics@3.3.6)(@types/node@20.19.1)(@types/react@18.3.23)(react@18.3.1):
    react-dnd: private
  react-dom@18.3.1(react@18.3.1):
    react-dom: private
  react-fast-compare@2.0.4:
    react-fast-compare: private
  react-helmet@6.1.0(react@18.3.1):
    react-helmet: private
  react-intl@6.6.2(react@18.3.1)(typescript@5.8.3):
    react-intl: private
  react-is@18.3.1:
    react-is: private
  react-query@3.39.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-query: private
  react-redux@8.1.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(redux@4.2.1):
    react-redux: private
  react-refresh@0.14.0:
    react-refresh: private
  react-remove-scroll-bar@2.3.8(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.5.10(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll: private
  react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-router-dom: private
  react-router@6.30.1(react@18.3.1):
    react-router: private
  react-select@5.8.0(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-select: private
  react-side-effect@2.1.2(react@18.3.1):
    react-side-effect: private
  react-style-singleton@2.2.3(@types/react@18.3.23)(react@18.3.1):
    react-style-singleton: private
  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-transition-group: private
  react-window@1.8.10(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-window: private
  react@18.3.1:
    react: private
  read-pkg-up@7.0.1:
    read-pkg-up: private
  read-pkg@5.2.0:
    read-pkg: private
  readable-stream@1.0.34:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  rechoir@0.8.0:
    rechoir: private
  redux-thunk@2.4.2(redux@4.2.1):
    redux-thunk: private
  redux@4.2.1:
    redux: private
  registry-auth-token@5.1.0:
    registry-auth-token: private
  registry-url@5.1.0:
    registry-url: private
  relateurl@0.2.7:
    relateurl: private
  remove-accents@0.5.0:
    remove-accents: private
  renderkid@3.0.0:
    renderkid: private
  request-compose@2.1.7:
    request-compose: private
  request-ip@3.3.0:
    request-ip: private
  request-multipart@1.0.0:
    request-multipart: private
  request-oauth@1.0.1:
    request-oauth: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  reselect@4.1.8:
    reselect: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-dir@1.0.1:
    resolve-dir: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-path@1.4.0:
    resolve-path: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve.exports@2.0.2:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  responselike@2.0.1:
    responselike: private
  restore-cursor@3.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rimraf@5.0.5:
    rimraf: private
  roarr@2.15.4:
    roarr: private
  rollup@4.44.1:
    rollup: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sanitize-html@2.13.0:
    sanitize-html: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  scroll-into-view-if-needed@2.2.31:
    scroll-into-view-if-needed: private
  semver-compare@1.0.0:
    semver-compare: private
  semver@7.5.4:
    semver: private
  sendmail@1.6.1:
    sendmail: private
  sentence-case@2.1.1:
    sentence-case: private
  serialize-error@7.0.1:
    serialize-error: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shallowequal@1.1.0:
    shallowequal: private
  sharp@0.34.2:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  shiki@0.14.7:
    shiki: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  sift@16.0.1:
    sift: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@4.0.1:
    simple-get: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  sirv@2.0.4:
    sirv: private
  slash@3.0.0:
    slash: private
  slate-history@0.93.0(slate@0.94.1):
    slate-history: private
  slate-react@0.98.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(slate@0.94.1):
    slate-react: private
  slate@0.94.1:
    slate: private
  snake-case@2.1.0:
    snake-case: private
  sorted-array-functions@1.3.0:
    sorted-array-functions: private
  source-list-map@2.0.1:
    source-list-map: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  spawn-command@0.0.2:
    spawn-command: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  sprintf-js@1.1.3:
    sprintf-js: private
  stack-trace@0.0.10:
    stack-trace: private
  stackframe@1.3.4:
    stackframe: private
  statuses@2.0.1:
    statuses: private
  stdin-discarder@0.2.2:
    stdin-discarder: private
  stream-chain@2.2.5:
    stream-chain: private
  stream-json@1.8.0:
    stream-json: private
  stream-slice@0.1.2:
    stream-slice: private
  streamsearch@1.1.0:
    streamsearch: private
  string-argv@0.3.2:
    string-argv: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@0.10.31:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@2.0.1:
    strip-json-comments: private
  style-loader@3.3.4(webpack@5.99.9(esbuild@0.25.5)):
    style-loader: private
  style-mod@4.1.2:
    style-mod: private
  styled-components@6.1.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    styled-components: private
  styled-jsx@5.1.6(react@19.1.0):
    styled-jsx: private
  stylis@4.3.2:
    stylis: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swap-case@1.1.2:
    swap-case: private
  tailwindcss@4.1.11:
    tailwindcss: private
  tapable@2.2.2:
    tapable: private
  tar-fs@2.1.3:
    tar-fs: private
  tar-stream@2.2.0:
    tar-stream: private
  tar@6.2.1:
    tar: private
  tarn@3.0.2:
    tarn: private
  terser-webpack-plugin@5.3.14(esbuild@0.25.5)(webpack@5.99.9(esbuild@0.25.5)):
    terser-webpack-plugin: private
  terser@5.43.1:
    terser: private
  text-hex@1.0.0:
    text-hex: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  through2@2.0.5:
    through2: private
  through@2.3.8:
    through: private
  tildify@2.0.0:
    tildify: private
  tiny-invariant@1.0.6:
    tiny-invariant: private
  tiny-warning@1.0.3:
    tiny-warning: private
  title-case@2.1.1:
    title-case: private
  tmp@0.0.33:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  toposort@2.0.2:
    toposort: private
  totalist@3.0.1:
    totalist: private
  touch@3.1.1:
    touch: private
  tree-kill@1.2.2:
    tree-kill: private
  triple-beam@1.4.1:
    triple-beam: private
  tslib@2.6.2:
    tslib: private
  tsscmp@1.0.6:
    tsscmp: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  turbo-darwin-64@2.5.4:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.4:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.4:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.4:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.4:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.4:
    turbo-windows-arm64: private
  type-fest@4.41.0:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: private
  typedoc-github-wiki-theme@1.1.0(typedoc-plugin-markdown@3.17.1(typedoc@0.25.10(typescript@5.8.3)))(typedoc@0.25.10(typescript@5.8.3)):
    typedoc-github-wiki-theme: private
  typedoc-plugin-markdown@3.17.1(typedoc@0.25.10(typescript@5.8.3)):
    typedoc-plugin-markdown: private
  typedoc@0.25.10(typescript@5.4.4):
    typedoc: private
  typescript@5.8.3:
    typescript: private
  uc.micro@1.0.6:
    uc.micro: private
  uglify-js@3.19.3:
    uglify-js: private
  umzug@3.8.1(@types/node@20.19.1):
    umzug: private
  unc-path-regex@0.1.2:
    unc-path-regex: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@6.21.0:
    undici-types: private
  undici@6.21.2:
    undici: private
  unique-string@2.0.0:
    unique-string: private
  universalify@2.0.1:
    universalify: private
  unload@2.2.0:
    unload: private
  unpipe@1.0.0:
    unpipe: private
  untildify@4.0.0:
    untildify: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  upper-case-first@1.1.2:
    upper-case-first: private
  upper-case@1.1.3:
    upper-case: private
  uri-js@4.4.1:
    uri-js: private
  url-join@4.0.1:
    url-join: private
  use-callback-ref@1.3.3(@types/react@18.3.23)(react@18.3.1):
    use-callback-ref: private
  use-context-selector@1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.0):
    use-context-selector: private
  use-isomorphic-layout-effect@1.2.1(@types/react@18.3.23)(react@18.3.1):
    use-isomorphic-layout-effect: private
  use-sidecar@1.1.3(@types/react@18.3.23)(react@18.3.1):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@18.3.1):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utila@0.4.0:
    utila: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@8.3.2:
    uuid: private
  v8flags@4.0.1:
    v8flags: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vary@1.1.2:
    vary: private
  vite@5.4.19(@types/node@20.19.1)(lightningcss@1.30.1)(terser@5.43.1):
    vite: private
  vscode-oniguruma@1.7.0:
    vscode-oniguruma: private
  vscode-textmate@8.0.0:
    vscode-textmate: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
  watchpack@2.4.4:
    watchpack: private
  wcwidth@1.0.1:
    wcwidth: private
  webpack-bundle-analyzer@4.10.2:
    webpack-bundle-analyzer: private
  webpack-dev-middleware@6.1.2(webpack@5.99.9(esbuild@0.25.5)):
    webpack-dev-middleware: private
  webpack-hot-middleware@2.26.1:
    webpack-hot-middleware: private
  webpack-sources@1.4.3:
    webpack-sources: private
  webpack@5.99.9(esbuild@0.25.5):
    webpack: private
  which-pm@2.0.0:
    which-pm: private
  which@2.0.2:
    which: private
  widest-line@3.1.0:
    widest-line: private
  winston-transport@4.9.0:
    winston-transport: private
  winston@3.10.0:
    winston: private
  wordwrap@1.0.0:
    wordwrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@3.0.3:
    write-file-atomic: private
  ws@8.17.1:
    ws: private
  xdg-app-paths@8.3.0:
    xdg-app-paths: private
  xdg-basedir@4.0.0:
    xdg-basedir: private
  xdg-portable@10.6.0:
    xdg-portable: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yalc@1.0.0-pre.53:
    yalc: private
  yallist@4.0.0:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yargs-parser@20.2.9:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  ylru@1.4.0:
    ylru: private
  yocto-queue@0.1.0:
    yocto-queue: private
  yoctocolors-cjs@2.1.2:
    yoctocolors-cjs: private
  yup@0.32.9:
    yup: private
  zod@3.24.2:
    zod: private
ignoredBuilds: []
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Thu, 26 Jun 2025 21:08:31 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/runtime@1.4.3'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@esbuild/win32-x64@0.21.5'
  - '@esbuild/win32-x64@0.25.5'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-arm64@0.34.2'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-darwin-x64@0.34.2'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm64@0.34.2'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-arm@0.34.2'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-s390x@0.34.2'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.34.2'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-wasm32@0.34.2'
  - '@img/sharp-win32-arm64@0.34.2'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-ia32@0.34.2'
  - '@img/sharp-win32-x64@0.33.5'
  - '@img/sharp-win32-x64@0.34.2'
  - '@next/swc-darwin-arm64@15.3.4'
  - '@next/swc-darwin-x64@15.3.4'
  - '@next/swc-linux-arm64-gnu@15.3.4'
  - '@next/swc-linux-arm64-musl@15.3.4'
  - '@next/swc-win32-arm64-msvc@15.3.4'
  - '@next/swc-win32-x64-msvc@15.3.4'
  - '@rollup/rollup-android-arm-eabi@4.44.1'
  - '@rollup/rollup-android-arm64@4.44.1'
  - '@rollup/rollup-darwin-arm64@4.44.1'
  - '@rollup/rollup-darwin-x64@4.44.1'
  - '@rollup/rollup-freebsd-arm64@4.44.1'
  - '@rollup/rollup-freebsd-x64@4.44.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.1'
  - '@rollup/rollup-linux-arm64-gnu@4.44.1'
  - '@rollup/rollup-linux-arm64-musl@4.44.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-musl@4.44.1'
  - '@rollup/rollup-linux-s390x-gnu@4.44.1'
  - '@rollup/rollup-win32-arm64-msvc@4.44.1'
  - '@rollup/rollup-win32-ia32-msvc@4.44.1'
  - '@rollup/rollup-win32-x64-msvc@4.44.1'
  - '@swc/core-darwin-arm64@1.12.7'
  - '@swc/core-darwin-x64@1.12.7'
  - '@swc/core-linux-arm-gnueabihf@1.12.7'
  - '@swc/core-linux-arm64-gnu@1.12.7'
  - '@swc/core-linux-arm64-musl@1.12.7'
  - '@swc/core-win32-arm64-msvc@1.12.7'
  - '@swc/core-win32-ia32-msvc@1.12.7'
  - '@swc/core-win32-x64-msvc@1.12.7'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.11'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
  - turbo-darwin-64@2.5.4
  - turbo-darwin-arm64@2.5.4
  - turbo-linux-arm64@2.5.4
  - turbo-windows-64@2.5.4
  - turbo-windows-arm64@2.5.4
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120

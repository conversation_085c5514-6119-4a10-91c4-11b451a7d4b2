# Phase 0 - Implémentation Technique : Fondations et Sécurité

## Vue d'ensemble
Cette phase établit les fondations techniques sécurisées et conformes RGPD pour l'ensemble de la plateforme CivicPoll.

## 1. Infrastructure de Base

### 1.1 Serveur et Environnement
```bash
# Configuration serveur Ubuntu 22.04 LTS
# Instance dédiée France : civicpoll.fr.smatflow.xyz

# Installation des prérequis
sudo apt update && sudo apt upgrade -y
sudo apt install -y nodejs npm postgresql postgresql-contrib postgis nginx git redis-server
```

### 1.2 Structure des Répertoires
```
/opt/civicpoll/
├── app/                    # Application Strapi
├── backups/               # Sauvegardes locales
├── logs/                  # Logs centralisés
├── ssl/                   # Certificats SSL
├── scripts/               # Scripts de maintenance
└── monitoring/            # Configuration monitoring
```

## 2. Sécurité et SSL/TLS

### 2.1 Configuration SSL avec Certbot
```bash
# Installation Certbot
sudo snap install --classic certbot
sudo ln -s /snap/bin/certbot /usr/bin/certbot

# Génération certificat
sudo certbot --nginx -d civicpoll.fr.smatflow.xyz
```

### 2.2 Configuration Nginx Sécurisée
```nginx
# /etc/nginx/sites-available/civicpoll
server {
    listen 443 ssl http2;
    server_name civicpoll.fr.smatflow.xyz;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/civicpoll.fr.smatflow.xyz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/civicpoll.fr.smatflow.xyz/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';" always;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
    
    location / {
        proxy_pass http://localhost:1337;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 2.3 WAF avec ModSecurity
```bash
# Installation ModSecurity
sudo apt install libapache2-mod-security2
# Configuration des règles OWASP CRS
```

## 3. Base de Données PostgreSQL

### 3.1 Configuration PostgreSQL avec PostGIS
```sql
-- Création de la base de données
CREATE DATABASE civicpoll_fr WITH ENCODING 'UTF8';
CREATE USER civicpoll_user WITH ENCRYPTED PASSWORD 'SECURE_PASSWORD_HERE';
GRANT ALL PRIVILEGES ON DATABASE civicpoll_fr TO civicpoll_user;

-- Activation PostGIS
\c civicpoll_fr
CREATE EXTENSION postgis;
CREATE EXTENSION pgcrypto; -- Pour chiffrement
```

### 3.2 Configuration Sécurité PostgreSQL
```bash
# /etc/postgresql/14/main/postgresql.conf
ssl = on
ssl_cert_file = '/etc/ssl/certs/ssl-cert-snakeoil.pem'
ssl_key_file = '/etc/ssl/private/ssl-cert-snakeoil.key'

# pg_hba.conf - Authentification forte
local   all   all                     scram-sha-256
host    all   all   127.0.0.1/32      scram-sha-256
```

## 4. Installation et Configuration Strapi

### 4.1 Création du Projet Strapi
```bash
cd /opt/civicpoll
npx create-strapi-app@latest app --quickstart --no-run
cd app

# Installation des dépendances spécifiques
npm install pg @strapi/provider-email-sendgrid
npm install strapi-plugin-sso strapi-plugin-audit-log
```

### 4.2 Configuration Environnement (.env)
```env
# /opt/civicpoll/app/.env
HOST=0.0.0.0
PORT=1337
APP_KEYS=GENERATE_SECURE_KEYS_HERE
API_TOKEN_SALT=GENERATE_SALT_HERE
ADMIN_JWT_SECRET=GENERATE_SECRET_HERE
JWT_SECRET=GENERATE_SECRET_HERE

# Database
DATABASE_CLIENT=postgres
DATABASE_HOST=127.0.0.1
DATABASE_PORT=5432
DATABASE_NAME=civicpoll_fr
DATABASE_USERNAME=civicpoll_user
DATABASE_PASSWORD=SECURE_PASSWORD_HERE
DATABASE_SSL=true

# Email
EMAIL_PROVIDER=sendgrid
EMAIL_SENDGRID_API_KEY=YOUR_SENDGRID_KEY

# RGPD
GDPR_ENABLED=true
GDPR_DATA_RETENTION_DAYS=365
GDPR_ANONYMIZE_ON_DELETE=true
```

### 4.3 Configuration Strapi Sécurisée
```javascript
// config/server.js
module.exports = ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  app: {
    keys: env.array('APP_KEYS'),
  },
  webhooks: {
    populateRelations: env.bool('WEBHOOKS_POPULATE_RELATIONS', false),
  },
});

// config/middlewares.js
module.exports = [
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': ["'self'", 'https:'],
          'img-src': ["'self'", 'data:', 'blob:', 'https:'],
          'media-src': ["'self'", 'data:', 'blob:', 'https:'],
        },
      },
    },
  },
  'strapi::cors',
  'strapi::poweredBy',
  'strapi::logger',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
```

## 5. RGPD Compliance

### 5.1 Plugin RGPD Personnalisé
```javascript
// src/plugins/gdpr/server/controllers/gdpr.js
module.exports = {
  async exportUserData(ctx) {
    const { userId } = ctx.params;
    // Logique d'export des données utilisateur
    const userData = await strapi.db.query('plugin::users-permissions.user').findOne({
      where: { id: userId },
      populate: ['responses', 'sessions', 'notifications'],
    });
    
    return {
      user: userData,
      exportDate: new Date(),
      format: 'json',
    };
  },

  async deleteUserData(ctx) {
    const { userId } = ctx.params;
    // Anonymisation avant suppression
    await strapi.db.query('plugin::users-permissions.user').update({
      where: { id: userId },
      data: {
        email: `deleted_${userId}@anonymous.local`,
        username: `deleted_user_${userId}`,
        // Conserver les réponses mais anonymisées
      },
    });
  },

  async getConsent(ctx) {
    // Récupération des consentements
  },
};
```

### 5.2 Audit Trail
```javascript
// src/extensions/audit-log/index.js
module.exports = {
  async log(event, data) {
    await strapi.db.query('api::audit-log.audit-log').create({
      data: {
        event,
        userId: data.userId,
        ipAddress: data.ip,
        userAgent: data.userAgent,
        timestamp: new Date(),
        details: JSON.stringify(data.details),
      },
    });
  },
};
```

## 6. CI/CD avec GitLab

### 6.1 GitLab CI Configuration
```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

variables:
  NODE_VERSION: "18"

# Tests
test:unit:
  stage: test
  image: node:$NODE_VERSION
  script:
    - npm ci
    - npm run test:unit
  coverage: '/Coverage: \d+\.\d+%/'

test:integration:
  stage: test
  image: node:$NODE_VERSION
  services:
    - postgres:14-alpine
  variables:
    POSTGRES_DB: test_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_pass
  script:
    - npm ci
    - npm run test:integration

# Security Scan
security:scan:
  stage: test
  image: node:$NODE_VERSION
  script:
    - npm audit --production
    - npm run lint:security

# Build
build:
  stage: build
  image: node:$NODE_VERSION
  script:
    - npm ci --production
    - npm run build
  artifacts:
    paths:
      - build/
    expire_in: 1 week

# Deploy
deploy:production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | ssh-add -
  script:
    - ssh -o StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_HOST "
        cd /opt/civicpoll/app &&
        git pull origin main &&
        npm ci --production &&
        npm run build &&
        pm2 restart civicpoll
      "
  only:
    - main
  environment:
    name: production
    url: https://civicpoll.fr.smatflow.xyz
```

## 7. Monitoring et Alerting

### 7.1 Prometheus Configuration
```yaml
# /opt/civicpoll/monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'civicpoll'
    static_configs:
      - targets: ['localhost:1337']
    metrics_path: '/api/metrics'

  - job_name: 'node_exporter'
    static_configs:
      - targets: ['localhost:9100']

  - job_name: 'postgres_exporter'
    static_configs:
      - targets: ['localhost:9187']
```

### 7.2 Grafana Dashboards
```json
// Dashboard configuration pour :
// - Performance API
// - Utilisation ressources
// - Erreurs et logs
// - Métriques RGPD
```

### 7.3 Alerting Rules
```yaml
# alerts.yml
groups:
  - name: civicpoll
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"

      - alert: DatabaseDown
        expr: up{job="postgres_exporter"} == 0
        for: 1m
        labels:
          severity: critical
```

## 8. Backup et Recovery

### 8.1 Script de Backup Automatisé
```bash
#!/bin/bash
# /opt/civicpoll/scripts/backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/civicpoll/backups"

# Backup Database
pg_dump -U civicpoll_user civicpoll_fr | gzip > $BACKUP_DIR/db_$DATE.sql.gz

# Backup Strapi Files
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /opt/civicpoll/app/public

# Retention Policy - Keep 30 days
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

# Upload to S3 (optional)
aws s3 sync $BACKUP_DIR s3://civicpoll-backups/fr/
```

### 8.2 Cron Configuration
```bash
# Backup quotidien à 3h du matin
0 3 * * * /opt/civicpoll/scripts/backup.sh
```

## 9. Documentation Technique

### 9.1 Architecture Decision Records (ADR)
```markdown
# ADR-001: Choix de PostgreSQL avec PostGIS
Date: 2024-01-20
Status: Accepté

## Contexte
Besoin de stockage géospatial pour le ciblage géographique.

## Décision
Utilisation de PostgreSQL avec extension PostGIS.

## Conséquences
- Performance optimale pour requêtes géospatiales
- Support natif dans Strapi
- Complexité accrue pour les backups
```

### 9.2 API Documentation
```javascript
// Configuration Swagger automatique dans Strapi
// Documentation générée à /documentation
```

## 10. Tests de Validation

### 10.1 Checklist de Sécurité
- [ ] SSL/TLS configuré et testé (SSL Labs A+)
- [ ] Headers de sécurité en place
- [ ] WAF opérationnel
- [ ] Rate limiting actif
- [ ] Logs d'audit fonctionnels

### 10.2 Tests de Performance
```bash
# Test de charge
ab -n 1000 -c 10 https://civicpoll.fr.smatflow.xyz/api/health

# Test SSL
openssl s_client -connect civicpoll.fr.smatflow.xyz:443
```

### 10.3 Tests RGPD
- [ ] Export de données utilisateur fonctionnel
- [ ] Suppression avec anonymisation
- [ ] Logs d'audit complets
- [ ] Consentements tracés

---

## ✅ VALIDATION FINALE DE LA PHASE 0

Une fois tous les éléments ci-dessus implémentés et testés :

1. **Exécuter la suite de tests complète**
   ```bash
   npm run test:all
   npm run test:security
   npm run test:rgpd
   ```

2. **Vérifier les métriques de sécurité**
   - SSL Labs : Score A minimum
   - OWASP ZAP : Aucune vulnérabilité critique
   - Lighthouse : Score sécurité > 90

3. **Valider la conformité RGPD**
   - Documentation des traitements
   - Processus d'export/suppression
   - Registre des consentements

4. **Effectuer un backup complet et tester la restauration**

5. **Documenter l'infrastructure**
   - Diagramme d'architecture
   - Procédures de maintenance
   - Plan de reprise d'activité

6. **Obtenir la validation du responsable sécurité**

**⚠️ NE PAS PASSER À LA PHASE 1 SANS VALIDATION COMPLÈTE DE TOUS CES POINTS**

---

*Document technique - Phase 0 - Version 1.0*
# Phase 2 - Implémentation Technique : Gamification et Engagement

## Vue d'ensemble
Cette phase implémente le système de gamification complet avec points, badges, niveaux et classements pour augmenter l'engagement des utilisateurs.

## 1. Content-Types Gamification

### 1.1 UserProfile (Extension du profil utilisateur)
```javascript
// api/user-profile/content-types/user-profile/schema.json
{
  "kind": "collectionType",
  "collectionName": "user_profiles",
  "info": {
    "singularName": "user-profile",
    "pluralName": "user-profiles",
    "displayName": "Profil Utilisateur"
  },
  "attributes": {
    "user": {
      "type": "relation",
      "relation": "oneToOne",
      "target": "plugin::users-permissions.user"
    },
    "points": {
      "type": "integer",
      "default": 0,
      "min": 0
    },
    "level": {
      "type": "integer",
      "default": 1,
      "min": 1
    },
    "experience": {
      "type": "integer",
      "default": 0,
      "min": 0
    },
    "badges": {
      "type": "relation",
      "relation": "manyToMany",
      "target": "api::badge.badge",
      "inversedBy": "users"
    },
    "achievements": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::achievement.achievement",
      "mappedBy": "user"
    },
    "streak": {
      "type": "integer",
      "default": 0,
      "description": "Jours consécutifs de participation"
    },
    "last_participation": {
      "type": "date"
    },
    "statistics": {
      "type": "json",
      "default": {
        "total_participations": 0,
        "polls_completed": 0,
        "questions_answered": 0,
        "average_completion_time": 0,
        "participation_rate": 0
      }
    },
    "preferences": {
      "type": "json",
      "default": {
        "show_on_leaderboard": true,
        "receive_challenge_notifications": true,
        "display_badges": true
      }
    }
  }
}
```

### 1.2 Badge
```javascript
// api/badge/content-types/badge/schema.json
{
  "kind": "collectionType",
  "collectionName": "badges",
  "info": {
    "singularName": "badge",
    "pluralName": "badges",
    "displayName": "Badge"
  },
  "attributes": {
    "name": {
      "type": "string",
      "required": true,
      "unique": true
    },
    "description": {
      "type": "text",
      "required": true
    },
    "category": {
      "type": "enumeration",
      "enum": ["participation", "quality", "community", "special", "geographic"],
      "required": true
    },
    "icon": {
      "type": "media",
      "required": true,
      "allowedTypes": ["images"]
    },
    "criteria": {
      "type": "json",
      "required": true,
      "description": "Conditions pour obtenir le badge"
    },
    "points_value": {
      "type": "integer",
      "default": 10,
      "min": 0
    },
    "rarity": {
      "type": "enumeration",
      "enum": ["common", "uncommon", "rare", "epic", "legendary"],
      "default": "common"
    },
    "users": {
      "type": "relation",
      "relation": "manyToMany",
      "target": "api::user-profile.user-profile",
      "mappedBy": "badges"
    },
    "active": {
      "type": "boolean",
      "default": true
    }
  }
}
```

### 1.3 Achievement (Réalisation)
```javascript
// api/achievement/content-types/achievement/schema.json
{
  "kind": "collectionType",
  "collectionName": "achievements",
  "info": {
    "singularName": "achievement",
    "pluralName": "achievements",
    "displayName": "Réalisation"
  },
  "attributes": {
    "user": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::user-profile.user-profile",
      "inversedBy": "achievements"
    },
    "badge": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::badge.badge"
    },
    "earned_at": {
      "type": "datetime",
      "required": true
    },
    "points_earned": {
      "type": "integer",
      "required": true
    },
    "context": {
      "type": "json",
      "description": "Contexte d'obtention (sondage, action, etc.)"
    }
  }
}
```

### 1.4 Challenge (Défi)
```javascript
// api/challenge/content-types/challenge/schema.json
{
  "kind": "collectionType",
  "collectionName": "challenges",
  "info": {
    "singularName": "challenge",
    "pluralName": "challenges",
    "displayName": "Défi"
  },
  "attributes": {
    "title": {
      "type": "string",
      "required": true
    },
    "description": {
      "type": "text",
      "required": true
    },
    "type": {
      "type": "enumeration",
      "enum": ["daily", "weekly", "monthly", "special"],
      "required": true
    },
    "criteria": {
      "type": "json",
      "required": true,
      "description": "Conditions de réussite du défi"
    },
    "reward": {
      "type": "component",
      "repeatable": false,
      "component": "gamification.reward"
    },
    "start_date": {
      "type": "datetime",
      "required": true
    },
    "end_date": {
      "type": "datetime",
      "required": true
    },
    "participants": {
      "type": "relation",
      "relation": "manyToMany",
      "target": "plugin::users-permissions.user"
    },
    "completions": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::challenge-completion.challenge-completion",
      "mappedBy": "challenge"
    },
    "geographic_zones": {
      "type": "relation",
      "relation": "manyToMany",
      "target": "api::geographic-zone.geographic-zone"
    }
  }
}
```

### 1.5 Leaderboard (Classement)
```javascript
// api/leaderboard/content-types/leaderboard/schema.json
{
  "kind": "collectionType",
  "collectionName": "leaderboards",
  "info": {
    "singularName": "leaderboard",
    "pluralName": "leaderboards",
    "displayName": "Classement"
  },
  "attributes": {
    "type": {
      "type": "enumeration",
      "enum": ["global", "regional", "departmental", "city", "monthly", "weekly"],
      "required": true
    },
    "geographic_zone": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::geographic-zone.geographic-zone"
    },
    "period_start": {
      "type": "date",
      "required": true
    },
    "period_end": {
      "type": "date",
      "required": true
    },
    "rankings": {
      "type": "json",
      "description": "Classement avec positions, utilisateurs et scores"
    },
    "last_updated": {
      "type": "datetime"
    }
  }
}
```

## 2. Système de Points et Niveaux

### 2.1 Service de Calcul des Points
```javascript
// src/api/gamification/services/points.js
module.exports = {
  POINT_VALUES: {
    POLL_COMPLETION: 10,
    FIRST_POLL: 50,
    STREAK_BONUS: 5,
    QUALITY_ANSWER: 5,
    QUICK_RESPONSE: 3,
    CHALLENGE_COMPLETION: 20,
    BADGE_EARNED: 0, // Points définis par badge
  },

  async awardPoints(userId, action, context = {}) {
    const userProfile = await strapi.entityService.findOne(
      'api::user-profile.user-profile',
      { filters: { user: userId }, populate: ['badges', 'achievements'] }
    );

    let pointsToAdd = this.POINT_VALUES[action] || 0;
    
    // Bonus multiplicateurs
    if (context.streak > 7) pointsToAdd *= 1.5;
    if (context.firstOfDay) pointsToAdd *= 2;
    
    // Mise à jour des points
    const newPoints = userProfile.points + Math.floor(pointsToAdd);
    
    await strapi.entityService.update('api::user-profile.user-profile', userProfile.id, {
      data: {
        points: newPoints,
        experience: userProfile.experience + Math.floor(pointsToAdd)
      }
    });

    // Vérifier le niveau
    await this.checkLevelUp(userProfile.id, newPoints);
    
    // Émettre un événement
    strapi.eventHub.emit('points.awarded', {
      userId,
      points: pointsToAdd,
      action,
      newTotal: newPoints
    });

    return { pointsAwarded: pointsToAdd, newTotal: newPoints };
  },

  async checkLevelUp(profileId, currentPoints) {
    const levels = [
      { level: 1, required: 0 },
      { level: 2, required: 100 },
      { level: 3, required: 300 },
      { level: 4, required: 600 },
      { level: 5, required: 1000 },
      { level: 6, required: 1500 },
      { level: 7, required: 2500 },
      { level: 8, required: 4000 },
      { level: 9, required: 6000 },
      { level: 10, required: 10000 }
    ];

    const profile = await strapi.entityService.findOne(
      'api::user-profile.user-profile', 
      profileId
    );

    const newLevel = levels.filter(l => l.required <= currentPoints).pop();
    
    if (newLevel.level > profile.level) {
      await strapi.entityService.update('api::user-profile.user-profile', profileId, {
        data: { level: newLevel.level }
      });

      // Badge de niveau
      await this.awardLevelBadge(profile.user, newLevel.level);
      
      // Notification
      await strapi.plugins['email'].services.email.send({
        to: profile.user.email,
        subject: `Félicitations ! Vous êtes maintenant niveau ${newLevel.level}`,
        html: `<h2>Bravo !</h2><p>Vous avez atteint le niveau ${newLevel.level} sur CivicPoll.</p>`
      });
    }
  }
};
```

### 2.2 Service de Badges
```javascript
// src/api/gamification/services/badges.js
module.exports = {
  async checkAndAwardBadges(userId, trigger, context = {}) {
    const badges = await strapi.entityService.findMany('api::badge.badge', {
      filters: { active: true },
      populate: ['users']
    });

    const userProfile = await strapi.entityService.findOne(
      'api::user-profile.user-profile',
      { filters: { user: userId }, populate: ['badges'] }
    );

    const earnedBadgeIds = userProfile.badges.map(b => b.id);

    for (const badge of badges) {
      if (earnedBadgeIds.includes(badge.id)) continue;

      if (await this.checkBadgeCriteria(badge, userId, trigger, context)) {
        await this.awardBadge(userId, badge.id);
      }
    }
  },

  async checkBadgeCriteria(badge, userId, trigger, context) {
    const criteria = badge.criteria;
    
    switch (criteria.type) {
      case 'poll_count':
        const stats = await this.getUserStats(userId);
        return stats.polls_completed >= criteria.value;
        
      case 'streak':
        return context.streak >= criteria.value;
        
      case 'geographic':
        return context.zones?.includes(criteria.zone);
        
      case 'speed':
        return context.completion_time <= criteria.max_seconds;
        
      case 'quality':
        return context.answer_quality >= criteria.min_quality;
        
      case 'special_event':
        return trigger === criteria.event;
        
      default:
        return false;
    }
  },

  async awardBadge(userId, badgeId) {
    const badge = await strapi.entityService.findOne('api::badge.badge', badgeId);
    const userProfile = await strapi.entityService.findOne(
      'api::user-profile.user-profile',
      { filters: { user: userId } }
    );

    // Créer la réalisation
    await strapi.entityService.create('api::achievement.achievement', {
      data: {
        user: userProfile.id,
        badge: badgeId,
        earned_at: new Date(),
        points_earned: badge.points_value,
        context: { trigger: 'automatic' }
      }
    });

    // Ajouter le badge au profil
    await strapi.entityService.update('api::user-profile.user-profile', userProfile.id, {
      data: {
        badges: [...userProfile.badges.map(b => b.id), badgeId]
      }
    });

    // Ajouter les points
    await strapi.service('api::gamification.points').awardPoints(
      userId, 
      'BADGE_EARNED', 
      { badgePoints: badge.points_value }
    );

    // Notification temps réel
    strapi.io.to(`user_${userId}`).emit('badge_earned', {
      badge: {
        name: badge.name,
        description: badge.description,
        icon: badge.icon,
        rarity: badge.rarity
      }
    });
  }
};
```

### 2.3 Défis et Challenges
```javascript
// src/api/gamification/services/challenges.js
module.exports = {
  async createDailyChallenges() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const dailyChallenges = [
      {
        title: "Participez à 3 sondages",
        description: "Complétez 3 sondages aujourd'hui",
        type: "daily",
        criteria: { type: "poll_count", count: 3 },
        reward: { points: 50, badge: null }
      },
      {
        title: "Réponse rapide",
        description: "Complétez un sondage en moins de 2 minutes",
        type: "daily",
        criteria: { type: "speed", max_seconds: 120 },
        reward: { points: 30 }
      },
      {
        title: "Explorateur local",
        description: "Participez à un sondage de votre ville",
        type: "daily",
        criteria: { type: "geographic", scope: "city" },
        reward: { points: 25 }
      }
    ];

    for (const challenge of dailyChallenges) {
      await strapi.entityService.create('api::challenge.challenge', {
        data: {
          ...challenge,
          start_date: today,
          end_date: tomorrow
        }
      });
    }
  },

  async checkChallengeCompletion(userId, action, context) {
    const activeChallenges = await strapi.entityService.findMany('api::challenge.challenge', {
      filters: {
        start_date: { $lte: new Date() },
        end_date: { $gte: new Date() }
      }
    });

    for (const challenge of activeChallenges) {
      if (await this.meetsCriteria(challenge.criteria, userId, action, context)) {
        await this.completeChallenge(userId, challenge.id);
      }
    }
  },

  async completeChallenge(userId, challengeId) {
    const challenge = await strapi.entityService.findOne('api::challenge.challenge', challengeId);
    
    // Vérifier si déjà complété
    const existing = await strapi.entityService.findMany('api::challenge-completion.challenge-completion', {
      filters: {
        user: userId,
        challenge: challengeId
      }
    });

    if (existing.length > 0) return;

    // Créer la complétion
    await strapi.entityService.create('api::challenge-completion.challenge-completion', {
      data: {
        user: userId,
        challenge: challengeId,
        completed_at: new Date()
      }
    });

    // Récompenses
    if (challenge.reward.points) {
      await strapi.service('api::gamification.points').awardPoints(
        userId, 
        'CHALLENGE_COMPLETION',
        { challengePoints: challenge.reward.points }
      );
    }

    if (challenge.reward.badge) {
      await strapi.service('api::gamification.badges').awardBadge(
        userId,
        challenge.reward.badge
      );
    }
  }
};
```

## 3. Frontend React - Composants Gamification

### 3.1 Composant Profil Gamifié
```jsx
// frontend/src/components/gamification/UserProfile.jsx
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { BadgeDisplay } from './BadgeDisplay';
import { LevelProgress } from './LevelProgress';
import { StatsOverview } from './StatsOverview';

export function UserProfile() {
  const [profile, setProfile] = useState(null);
  const [selectedBadge, setSelectedBadge] = useState(null);

  useEffect(() => {
    loadProfile();
    subscribeToUpdates();
  }, []);

  const loadProfile = async () => {
    const response = await fetch(`${API_BASE_URL}/user-profiles/me`, {
      credentials: 'include'
    });
    const data = await response.json();
    setProfile(data.data);
  };

  const subscribeToUpdates = () => {
    window.socket.on('points_updated', (data) => {
      setProfile(prev => ({
        ...prev,
        points: data.newTotal,
        experience: data.experience
      }));
    });

    window.socket.on('badge_earned', (data) => {
      // Animation de nouveau badge
      showBadgeNotification(data.badge);
      loadProfile(); // Recharger le profil
    });
  };

  return (
    <div className="user-profile gamified">
      <div className="profile-header">
        <div className="avatar-section">
          <img src={profile?.avatar || '/default-avatar.png'} alt="Avatar" />
          <div className="level-badge">
            <span className="level">Niveau {profile?.level}</span>
          </div>
        </div>
        
        <div className="profile-info">
          <h2>{profile?.user.username}</h2>
          <p className="location">{profile?.user.location.city}</p>
          
          <LevelProgress 
            level={profile?.level}
            experience={profile?.experience}
            nextLevelExp={getNextLevelExp(profile?.level)}
          />
          
          <div className="points-display">
            <motion.div
              key={profile?.points}
              initial={{ scale: 1.2 }}
              animate={{ scale: 1 }}
              className="points"
            >
              {profile?.points || 0} points
            </motion.div>
          </div>
        </div>
      </div>

      <div className="stats-section">
        <h3>Statistiques</h3>
        <StatsOverview stats={profile?.statistics} />
      </div>

      <div className="badges-section">
        <h3>Badges ({profile?.badges?.length || 0})</h3>
        <div className="badges-grid">
          {profile?.badges?.map(badge => (
            <BadgeDisplay
              key={badge.id}
              badge={badge}
              onClick={() => setSelectedBadge(badge)}
              size="medium"
              showProgress={!badge.earned}
            />
          ))}
        </div>
      </div>

      <div className="achievements-timeline">
        <h3>Dernières Réalisations</h3>
        {profile?.achievements?.slice(0, 5).map(achievement => (
          <div key={achievement.id} className="achievement-item">
            <img src={achievement.badge.icon.url} alt={achievement.badge.name} />
            <div>
              <strong>{achievement.badge.name}</strong>
              <span className="date">
                {new Date(achievement.earned_at).toLocaleDateString()}
              </span>
            </div>
            <span className="points">+{achievement.points_earned} pts</span>
          </div>
        ))}
      </div>
    </div>
  );
}
```

### 3.2 Système de Notifications en Temps Réel
```jsx
// frontend/src/components/gamification/NotificationSystem.jsx
import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import confetti from 'canvas-confetti';

export function NotificationSystem() {
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    // Connexion WebSocket
    window.socket.on('badge_earned', (data) => {
      showBadgeNotification(data);
    });

    window.socket.on('level_up', (data) => {
      showLevelUpNotification(data);
    });

    window.socket.on('challenge_completed', (data) => {
      showChallengeNotification(data);
    });
  }, []);

  const showBadgeNotification = (badge) => {
    const notification = {
      id: Date.now(),
      type: 'badge',
      title: 'Nouveau Badge !',
      message: badge.name,
      icon: badge.icon.url,
      rarity: badge.rarity
    };

    setNotifications(prev => [...prev, notification]);
    
    // Confettis pour les badges rares
    if (['epic', 'legendary'].includes(badge.rarity)) {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
    }

    // Auto-remove après 5 secondes
    setTimeout(() => {
      removeNotification(notification.id);
    }, 5000);
  };

  const showLevelUpNotification = (data) => {
    const notification = {
      id: Date.now(),
      type: 'level',
      title: 'Niveau Supérieur !',
      message: `Vous êtes maintenant niveau ${data.newLevel}`,
      icon: '/icons/level-up.svg'
    };

    setNotifications(prev => [...prev, notification]);
    
    // Animation spéciale
    confetti({
      particleCount: 200,
      spread: 180,
      colors: ['#FFD700', '#FFA500', '#FF6347']
    });
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <div className="notification-container">
      <AnimatePresence>
        {notifications.map(notification => (
          <motion.div
            key={notification.id}
            initial={{ x: 300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: 300, opacity: 0 }}
            className={`notification ${notification.type} ${notification.rarity}`}
          >
            <img src={notification.icon} alt="" />
            <div className="notification-content">
              <h4>{notification.title}</h4>
              <p>{notification.message}</p>
            </div>
            <button onClick={() => removeNotification(notification.id)}>×</button>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}
```

### 3.3 Dashboard de Défis
```jsx
// frontend/src/components/gamification/ChallengesBoard.jsx
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { CircularProgress } from './CircularProgress';

export function ChallengesBoard() {
  const [challenges, setChallenges] = useState({
    daily: [],
    weekly: [],
    monthly: []
  });

  useEffect(() => {
    loadChallenges();
  }, []);

  const loadChallenges = async () => {
    const response = await fetch(`${API_BASE_URL}/challenges/active`, {
      credentials: 'include'
    });
    const data = await response.json();
    
    // Grouper par type
    const grouped = data.data.reduce((acc, challenge) => {
      acc[challenge.type].push(challenge);
      return acc;
    }, { daily: [], weekly: [], monthly: [] });
    
    setChallenges(grouped);
  };

  return (
    <div className="challenges-board">
      <h2>Défis Actifs</h2>
      
      <div className="challenges-tabs">
        <Tab 
          title="Quotidiens" 
          challenges={challenges.daily}
          icon="🌟"
          resetTime="Réinitialisation dans 8h"
        />
        <Tab 
          title="Hebdomadaires" 
          challenges={challenges.weekly}
          icon="🏆"
          resetTime="Réinitialisation dans 3j"
        />
        <Tab 
          title="Mensuels" 
          challenges={challenges.monthly}
          icon="👑"
          resetTime="Réinitialisation dans 15j"
        />
      </div>
    </div>
  );
}

function Tab({ title, challenges, icon, resetTime }) {
  return (
    <div className="challenge-tab">
      <div className="tab-header">
        <h3>{icon} {title}</h3>
        <span className="reset-time">{resetTime}</span>
      </div>
      
      <div className="challenges-list">
        {challenges.map(challenge => (
          <ChallengeCard key={challenge.id} challenge={challenge} />
        ))}
      </div>
    </div>
  );
}

function ChallengeCard({ challenge }) {
  const progress = calculateProgress(challenge);
  
  return (
    <motion.div 
      className="challenge-card"
      whileHover={{ scale: 1.02 }}
    >
      <div className="challenge-info">
        <h4>{challenge.title}</h4>
        <p>{challenge.description}</p>
        <div className="reward">
          🎁 {challenge.reward.points} points
          {challenge.reward.badge && ' + Badge spécial'}
        </div>
      </div>
      
      <CircularProgress 
        progress={progress}
        size={60}
        strokeWidth={6}
      />
    </motion.div>
  );
}
```

### 3.4 Classements
```jsx
// frontend/src/components/gamification/Leaderboard.jsx
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export function Leaderboard() {
  const [activeTab, setActiveTab] = useState('global');
  const [timeframe, setTimeframe] = useState('monthly');
  const [rankings, setRankings] = useState([]);
  const [userRank, setUserRank] = useState(null);

  useEffect(() => {
    loadLeaderboard();
  }, [activeTab, timeframe]);

  const loadLeaderboard = async () => {
    const response = await fetch(
      `${API_BASE_URL}/leaderboards?type=${activeTab}&timeframe=${timeframe}`,
      { credentials: 'include' }
    );
    const data = await response.json();
    setRankings(data.rankings);
    setUserRank(data.userRank);
  };

  return (
    <div className="leaderboard">
      <h2>Classements</h2>
      
      <div className="leaderboard-controls">
        <div className="tabs">
          <button 
            className={activeTab === 'global' ? 'active' : ''}
            onClick={() => setActiveTab('global')}
          >
            🌍 Global
          </button>
          <button 
            className={activeTab === 'regional' ? 'active' : ''}
            onClick={() => setActiveTab('regional')}
          >
            🗺️ Régional
          </button>
          <button 
            className={activeTab === 'city' ? 'active' : ''}
            onClick={() => setActiveTab('city')}
          >
            🏙️ Ville
          </button>
        </div>
        
        <select 
          value={timeframe} 
          onChange={(e) => setTimeframe(e.target.value)}
        >
          <option value="weekly">Cette semaine</option>
          <option value="monthly">Ce mois</option>
          <option value="alltime">Tout temps</option>
        </select>
      </div>

      <div className="podium">
        {rankings.slice(0, 3).map((user, index) => (
          <motion.div
            key={user.id}
            className={`podium-place place-${index + 1}`}
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: index * 0.1 }}
          >
            <div className="medal">{['🥇', '🥈', '🥉'][index]}</div>
            <img src={user.avatar} alt={user.username} />
            <h4>{user.username}</h4>
            <p className="points">{user.points} pts</p>
            <div className="badges-preview">
              {user.topBadges.slice(0, 3).map(badge => (
                <img 
                  key={badge.id} 
                  src={badge.icon} 
                  alt={badge.name}
                  title={badge.name}
                />
              ))}
            </div>
          </motion.div>
        ))}
      </div>

      <div className="rankings-list">
        {rankings.slice(3).map((user, index) => (
          <motion.div 
            key={user.id}
            className={`ranking-item ${user.isCurrentUser ? 'current-user' : ''}`}
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: index * 0.05 }}
          >
            <span className="rank">#{index + 4}</span>
            <img src={user.avatar} alt={user.username} />
            <span className="username">{user.username}</span>
            <span className="level">Niv. {user.level}</span>
            <span className="points">{user.points} pts</span>
          </motion.div>
        ))}
      </div>

      {userRank && userRank.position > 10 && (
        <div className="user-rank-summary">
          <div className="divider">...</div>
          <div className="ranking-item current-user">
            <span className="rank">#{userRank.position}</span>
            <img src={userRank.avatar} alt="Vous" />
            <span className="username">Vous</span>
            <span className="level">Niv. {userRank.level}</span>
            <span className="points">{userRank.points} pts</span>
          </div>
        </div>
      )}
    </div>
  );
}
```

## 4. WebSocket et Temps Réel

### 4.1 Configuration Socket.IO
```javascript
// src/index.js (Strapi)
const { Server } = require('socket.io');

module.exports = {
  register({ strapi }) {
    const io = new Server(strapi.server.httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL,
        credentials: true
      }
    });

    io.on('connection', (socket) => {
      console.log('User connected:', socket.id);

      // Authentification
      socket.on('authenticate', async (token) => {
        try {
          const { id } = await strapi.plugins['users-permissions']
            .services.jwt.verify(token);
          
          socket.userId = id;
          socket.join(`user_${id}`);
          
          // Rejoindre les rooms géographiques
          const userLocation = await strapi.entityService.findOne(
            'api::user-location.user-location',
            { filters: { user: id }, populate: ['geographic_zone'] }
          );
          
          if (userLocation?.geographic_zone) {
            socket.join(`zone_${userLocation.geographic_zone.id}`);
          }
        } catch (error) {
          socket.emit('auth_error', 'Invalid token');
        }
      });

      socket.on('disconnect', () => {
        console.log('User disconnected:', socket.id);
      });
    });

    strapi.io = io;
  }
};
```

### 4.2 Broadcasting des Événements
```javascript
// src/api/gamification/services/events.js
module.exports = {
  broadcast(event, data, target = 'all') {
    switch (target.type) {
      case 'user':
        strapi.io.to(`user_${target.id}`).emit(event, data);
        break;
        
      case 'zone':
        strapi.io.to(`zone_${target.id}`).emit(event, data);
        break;
        
      case 'all':
        strapi.io.emit(event, data);
        break;
    }
  },

  // Événements spécifiques
  notifyBadgeEarned(userId, badge) {
    this.broadcast('badge_earned', {
      badge: {
        id: badge.id,
        name: badge.name,
        description: badge.description,
        icon: badge.icon,
        rarity: badge.rarity
      },
      timestamp: new Date()
    }, { type: 'user', id: userId });
  },

  notifyLeaderboardUpdate(zone) {
    this.broadcast('leaderboard_updated', {
      zone: zone.id,
      type: zone.type
    }, { type: 'zone', id: zone.id });
  },

  notifyChallengeProgress(userId, challenge, progress) {
    this.broadcast('challenge_progress', {
      challengeId: challenge.id,
      progress,
      completed: progress >= 100
    }, { type: 'user', id: userId });
  }
};
```

## 5. Badges Prédéfinis

### 5.1 Script d'Import des Badges
```javascript
// scripts/import-badges.js
const badges = [
  // Badges de Participation
  {
    name: "Premier Pas",
    description: "Complétez votre premier sondage",
    category: "participation",
    criteria: { type: "poll_count", value: 1 },
    points_value: 50,
    rarity: "common"
  },
  {
    name: "Habitué",
    description: "Participez à 10 sondages",
    category: "participation",
    criteria: { type: "poll_count", value: 10 },
    points_value: 100,
    rarity: "uncommon"
  },
  {
    name: "Expert",
    description: "Participez à 50 sondages",
    category: "participation",
    criteria: { type: "poll_count", value: 50 },
    points_value: 500,
    rarity: "rare"
  },
  
  // Badges de Régularité
  {
    name: "Régulier",
    description: "Participez 7 jours consécutifs",
    category: "participation",
    criteria: { type: "streak", value: 7 },
    points_value: 200,
    rarity: "uncommon"
  },
  {
    name: "Dévoué",
    description: "Participez 30 jours consécutifs",
    category: "participation",
    criteria: { type: "streak", value: 30 },
    points_value: 1000,
    rarity: "epic"
  },
  
  // Badges Géographiques
  {
    name: "Citoyen Local",
    description: "Participez à 5 sondages de votre ville",
    category: "geographic",
    criteria: { type: "geographic", scope: "city", count: 5 },
    points_value: 150,
    rarity: "uncommon"
  },
  {
    name: "Explorateur Régional",
    description: "Participez à des sondages dans 3 départements différents",
    category: "geographic",
    criteria: { type: "geographic_diversity", departments: 3 },
    points_value: 300,
    rarity: "rare"
  },
  
  // Badges de Qualité
  {
    name: "Réponses Détaillées",
    description: "Donnez 10 réponses textuelles de qualité",
    category: "quality",
    criteria: { type: "quality_answers", count: 10 },
    points_value: 250,
    rarity: "rare"
  },
  
  // Badges Spéciaux
  {
    name: "Pionnier",
    description: "Fait partie des 100 premiers utilisateurs",
    category: "special",
    criteria: { type: "special_event", event: "early_adopter" },
    points_value: 500,
    rarity: "legendary"
  },
  {
    name: "Nocturne",
    description: "Participez à un sondage entre minuit et 6h",
    category: "special",
    criteria: { type: "time_based", hours: [0, 1, 2, 3, 4, 5] },
    points_value: 100,
    rarity: "uncommon"
  }
];

async function importBadges() {
  for (const badge of badges) {
    await strapi.entityService.create('api::badge.badge', {
      data: {
        ...badge,
        icon: await uploadBadgeIcon(badge.name)
      }
    });
  }
}
```

## 6. Analytics Gamification

### 6.1 Tracking des Métriques
```javascript
// src/api/gamification/services/analytics.js
module.exports = {
  async trackGamificationMetrics() {
    const metrics = {
      engagement: {
        daily_active_users: await this.getDailyActiveUsers(),
        avg_session_duration: await this.getAverageSessionDuration(),
        retention_rate: await this.getRetentionRate(),
        completion_rate: await this.getCompletionRate()
      },
      gamification: {
        badges_earned_today: await this.getBadgesEarnedToday(),
        avg_points_per_user: await this.getAveragePointsPerUser(),
        challenge_completion_rate: await this.getChallengeCompletionRate(),
        leaderboard_participation: await this.getLeaderboardParticipation()
      },
      distribution: {
        users_by_level: await this.getUsersByLevel(),
        badges_distribution: await this.getBadgesDistribution(),
        geographic_activity: await this.getGeographicActivity()
      }
    };

    // Sauvegarder les métriques
    await strapi.entityService.create('api::analytics.analytics', {
      data: {
        type: 'gamification_daily',
        date: new Date(),
        metrics
      }
    });

    return metrics;
  },

  async getDailyActiveUsers() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return strapi.db.query('api::user-profile.user-profile').count({
      where: {
        last_participation: { $gte: today }
      }
    });
  },

  async getRetentionRate() {
    // Calcul du taux de rétention J7/J30
    // ...
  }
};
```

## 7. Tests Gamification

### 7.1 Tests Unitaires
```javascript
// tests/gamification/points.test.js
describe('Points System', () => {
  let user;
  let profile;

  beforeEach(async () => {
    user = await createTestUser();
    profile = await createUserProfile(user.id);
  });

  test('Should award points for poll completion', async () => {
    const result = await strapi.service('api::gamification.points')
      .awardPoints(user.id, 'POLL_COMPLETION');

    expect(result.pointsAwarded).toBe(10);
    expect(result.newTotal).toBe(10);
  });

  test('Should apply streak bonus', async () => {
    // Simuler une série de 10 jours
    await updateUserProfile(profile.id, { streak: 10 });

    const result = await strapi.service('api::gamification.points')
      .awardPoints(user.id, 'POLL_COMPLETION', { streak: 10 });

    expect(result.pointsAwarded).toBe(15); // 10 * 1.5
  });

  test('Should trigger level up', async () => {
    // Donner 95 points (proche du niveau 2)
    await updateUserProfile(profile.id, { points: 95, level: 1 });

    const result = await strapi.service('api::gamification.points')
      .awardPoints(user.id, 'POLL_COMPLETION');

    const updatedProfile = await getUserProfile(profile.id);
    expect(updatedProfile.level).toBe(2);
  });
});

describe('Badge System', () => {
  test('Should award "First Step" badge', async () => {
    const user = await createTestUser();
    
    await strapi.service('api::gamification.badges')
      .checkAndAwardBadges(user.id, 'poll_completed', { pollCount: 1 });

    const profile = await getUserProfile(user.id);
    expect(profile.badges).toHaveLength(1);
    expect(profile.badges[0].name).toBe('Premier Pas');
  });
});
```

## 8. Migration et Déploiement

### 8.1 Script de Migration Phase 2
```bash
#!/bin/bash
# deploy-phase2.sh

echo "🎮 Déploiement Phase 2 - Gamification..."

# 1. Backup
./scripts/backup.sh

# 2. Update code
cd /opt/civicpoll/app
git pull origin main

# 3. Install new dependencies
npm ci --production

# 4. Run migrations
npm run strapi migrate

# 5. Import badges
node scripts/import-badges.js

# 6. Initialize user profiles
node scripts/migrate-user-profiles.js

# 7. Build frontend with gamification
cd frontend
npm ci
npm run build

# 8. Restart with Socket.IO
pm2 restart civicpoll --update-env

# 9. Start cron jobs
pm2 start ecosystem.config.js --only civicpoll-cron

echo "✅ Phase 2 déployée avec succès"
```

### 8.2 Cron Jobs Gamification
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'civicpoll-cron',
    script: './cron/index.js',
    cron_restart: '0 0 * * *',
    env: {
      NODE_ENV: 'production'
    }
  }]
};

// cron/index.js
const cron = require('node-cron');

// Défis quotidiens - Minuit
cron.schedule('0 0 * * *', async () => {
  await strapi.service('api::gamification.challenges').createDailyChallenges();
  await strapi.service('api::gamification.streaks').resetExpiredStreaks();
});

// Calcul des classements - Toutes les heures
cron.schedule('0 * * * *', async () => {
  await strapi.service('api::gamification.leaderboard').updateAllLeaderboards();
});

// Analytics - 3h du matin
cron.schedule('0 3 * * *', async () => {
  await strapi.service('api::gamification.analytics').trackGamificationMetrics();
});
```

---

## ✅ VALIDATION FINALE DE LA PHASE 2

### Tests Fonctionnels
- [ ] Système de points opérationnel
- [ ] Attribution automatique des badges
- [ ] Défis quotidiens/hebdomadaires actifs
- [ ] Classements temps réel
- [ ] Notifications WebSocket
- [ ] Animations et feedback visuels

### Tests Performance
- [ ] WebSocket < 50ms latence
- [ ] Calcul classements < 2s
- [ ] Charge 1000 users simultanés

### Tests UX
- [ ] Onboarding gamification clair
- [ ] Progression visible
- [ ] Récompenses motivantes

---

*Document technique - Phase 2 - Version 1.0*
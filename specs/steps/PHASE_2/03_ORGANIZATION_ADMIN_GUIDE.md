# Phase 2 - Guide Administrateur Organisation : Plus de Possibilités

## Introduction
La Phase 2 enrichit considérablement vos possibilités ! Découvrez les 20 types de questions, la bibliothèque de templates professionnels, l'API pour vos intégrations et les notifications multi-canal.

## 1. Les 20 Types de Questions à Votre Disposition

### 1.1 Questions de Choix (3 types)

**Choix Unique** ⭕
```
Q: Quel est votre moyen de transport principal ?
○ Voiture
○ Transport en commun  
○ Vélo
○ Marche
```
*Idéal pour : décisions binaires, préférences claires*

**Choix Multiple** ☑️
```
Q: Quels services utilisez-vous ? (plusieurs réponses possibles)
☐ Bibliothèque
☐ Piscine municipale
☐ Espaces verts
☐ Centre culturel
☐ Maison des associations
```
*Idéal pour : usages multiples, inventaires*

**Liste Déroulante** 📋
```
Q: Votre département :
[▼ Sélectionnez...     ]
```
*Idéal pour : longues listes, gain de place*

### 1.2 Questions Textuelles (5 types)

**Texte Court** 📝
- Max 255 caractères
- Réponses concises
- *Usage : noms, titres, mots-clés*

**Texte Long** 📄
- Max 5000 caractères  
- Réponses détaillées
- *Usage : commentaires, suggestions, témoignages*

**Email** 📧
- Validation automatique format
- *Usage : contact, newsletter*

**Téléphone** 📱
- Formats internationaux supportés
- *Usage : rappel, SMS*

**Nombre** 🔢
- Entiers ou décimaux
- Min/max configurables
- *Usage : âge, quantités, budgets*

### 1.3 Questions de Notation (4 types)

**Échelle de Likert** 📊
```
Très insatisfait  1  2  3  4  5  Très satisfait
                  ○  ○  ●  ○  ○
```
*Classique pour satisfaction*

**Notation Étoiles** ⭐
```
Comment évaluez-vous ce service ?
★★★★☆
```
*Visuel et intuitif*

**Score NPS** 📈
```
Probabilité de recommander ? 
0  1  2  3  4  5  6  7  8  9  10
```
*Méthodologie Net Promoter Score*

**Curseur** 🎚️
```
Budget souhaité :
|----●--------| 3500€
0€          10000€
```
*Pour valeurs continues*

### 1.4 Questions de Classement (2 types)

**Classement par Ordre** 🏆
```
Classez par priorité (glisser-déposer) :
1. ═══ Sécurité
2. ═══ Propreté  
3. ═══ Animation
4. ═══ Accessibilité
```
*Pour priorisation*

**Matrice de Choix** 📋
```
              Très important | Important | Peu important
Sécurité              ●             ○            ○
Propreté              ○             ●            ○  
Animation             ○             ○            ●
```
*Pour évaluations multiples*

### 1.5 Questions Médias (3 types)

**Upload Image** 🖼️
- Formats : JPG, PNG, WebP
- Max 5MB
- *Usage : photos situations, preuves visuelles*

**Upload Fichier** 📎
- Tous formats documents
- Max 10MB
- *Usage : plans, devis, justificatifs*

**Réponse Vidéo** 🎥
- Max 2 minutes
- *Usage : témoignages, démonstrations*

### 1.6 Questions Avancées (3 types)

**Signature** ✍️
- Dessin tactile/souris
- Validation légale
- *Usage : consentements, engagements*

**Dessin** 🎨
- Croquis libre
- Annotations possibles
- *Usage : idées visuelles, plans*

**Géolocalisation** 📍
```
Cliquez sur la carte pour indiquer le lieu
[Carte interactive]
```
*Usage : signalements, suggestions localisées*

### 1.7 Stratégies d'Utilisation

**Combiner pour enrichir** :
```
1. Question choix → Si "Autre", texte court
2. Note faible → Texte "Pourquoi ?"
3. Géolocalisation → Photo du lieu
```

**Progression logique** :
```
Simple → Complexe → Personnel
Choix → Notation → Commentaire
```

## 2. Bibliothèque de Templates Professionnels

### 2.1 Templates RH/Management

**Satisfaction Employés (eNPS)**
- 12 questions optimisées
- Score calculé automatiquement
- Benchmarks sectoriels
- *Temps : 8 minutes*

**Évaluation 360°**
- Auto-évaluation + pairs
- Compétences prédéfinies
- Matrices de notation
- *Personnalisable par rôle*

**Onboarding Feedback**
- Checkpoints J7, J30, J90
- Questions évolutives
- Détection points friction
- *Améliore intégration*

### 2.2 Templates Marketing/Commercial

**NPS - Net Promoter Score**
```
Structure type :
1. Score recommandation (0-10)
2. Raison principale (choix multiple)
3. Suggestions amélioration (texte)
4. Données démographiques
```
*ROI prouvé sur fidélisation*

**Test de Concept**
- Présentation visuelle
- Échelles d'intérêt
- Prix psychologique
- Intentions d'achat
- *Valide avant lancement*

**CSAT Post-Achat**
- Déclenchement automatique J+3
- Questions contextuelles
- Demande avis détaillé si insatisfait
- *Taux réponse 45% en moyenne*

### 2.3 Templates Consultation Publique

**Budget Participatif**
```
1. Information projet (lecture)
2. Vote pour/contre
3. Si pour : allocation budget (curseur)
4. Si contre : alternatives (choix multiple)
5. Suggestions (texte + géoloc)
```
*Engagement citoyen maximal*

**Aménagement Urbain**
- Plans visuels intégrés
- Zones d'impact sur carte
- Préoccupations hiérarchisées
- Upload photos situations
- *Consultation complète*

**Évaluation Service Public**
- Par service utilisé
- Fréquence utilisation
- Points satisfaction/friction
- Comparaison N-1
- *Pilotage amélioration continue*

### 2.4 Templates Événementiels

**Inscription Enrichie**
```
- Infos participant
- Choix sessions/ateliers
- Préférences alimentaires
- Besoins accessibilité
- Questions au conférencier
```
*Tout en un formulaire*

**Feedback Post-Event**
- Note globale
- Par session/intervenant
- Logistique détaillée
- Recommandation
- Photos souvenirs
- *Insights pour prochaine édition*

### 2.5 Utiliser les Templates

**Méthode rapide** :
1. **Parcourir** la galerie par catégorie
2. **Prévisualiser** le template complet
3. **Utiliser** tel quel ou personnaliser
4. **Sauvegarder** vos variantes

**Personnalisation** :
- Ajouter/supprimer questions
- Modifier formulations
- Changer types questions
- Adapter charte graphique

**Création template maison** :
1. Partir d'un sondage réussi
2. **Actions → Sauver comme template**
3. Documenter usage
4. Réutiliser à volonté

## 3. API Publique - Intégrez CivicPoll

### 3.1 Pourquoi Utiliser l'API ?

**Automatisation** 🤖
- Créer sondages depuis votre CRM
- Importer participants en masse
- Exporter résultats temps réel
- Déclencher sur événements

**Intégration** 🔗
- Widget sur votre site
- App mobile native
- Tableaux de bord BI
- Workflows automatisés

**Innovation** 💡
- Cas d'usage sur mesure
- Expériences uniques
- Données enrichies
- Écosystème étendu

### 3.2 Obtenir Votre Clé API

1. **Dashboard → Paramètres → API**
2. **"Générer nouvelle clé"**
3. Nommez l'application
4. Sélectionnez permissions :
   - ✅ Lecture sondages
   - ✅ Création sondages
   - ✅ Lecture réponses
   - ⚠️ Modification (prudence)

**Clé générée** :
```
civicpoll_live_sk_9f8e7d6c5b4a3...
```

⚠️ **Sécurité** : 
- Copiez immédiatement
- Ne partagez jamais
- Stockez sécurisé
- Rotation régulière

### 3.3 Exemples d'Utilisation

**Créer un sondage via API** :
```bash
curl -X POST https://civicpoll.fr.smatflow.xyz/api/v1/polls \
  -H "X-API-Key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Satisfaction Formation",
    "description": "Votre avis nous intéresse",
    "questions": [...],
    "geographic_zones": ["lyon"]
  }'
```

**Récupérer les résultats** :
```javascript
const response = await fetch('/api/v1/polls/123/analytics', {
  headers: {
    'X-API-Key': 'YOUR_API_KEY'
  }
});

const analytics = await response.json();
// { total_responses: 234, completion_rate: 0.87, ... }
```

**Webhooks pour événements** :
```
Configurez une URL pour recevoir :
- Nouveau participant
- Sondage complété  
- Seuil atteint
- Analyse disponible
```

### 3.4 Cas Concrets d'API

**Site Web** 💻
```html
<!-- Widget embarqué -->
<div id="civicpoll-widget" 
     data-poll-id="abc123"
     data-theme="light">
</div>
<script src="https://civicpoll.fr/widget.js"></script>
```

**Application Mobile** 📱
```swift
// SDK iOS
CivicPoll.configure(apiKey: "YOUR_KEY")
let poll = try await CivicPoll.polls.get("abc123")
```

**Automation Zapier** ⚡
```
Trigger : Email reçu avec tag "Feedback"
Action : Créer sondage CivicPoll
Config : Template "CSAT", 48h durée
Result : Lien envoyé automatiquement
```

**Dashboard Power BI** 📊
- Connecteur natif CivicPoll
- Refresh toutes les heures
- Drill-down géographique
- Alertes sur seuils

### 3.5 Limites et Quotas

**Plan de base** :
- 100 requêtes/heure
- 10 sondages/mois via API
- Webhooks : 5 URLs

**Plan Pro** :
- 1000 requêtes/heure
- Illimité sondages
- Webhooks : 50 URLs
- Support prioritaire

**Monitoring** :
```
Utilisation API ce mois :
├── Requêtes : 12,456/30,000
├── Sondages créés : 45
├── Data transférée : 234 MB
└── Coût estimé : Inclus
```

## 4. Notifications Multi-Canal

### 4.1 Vue d'Ensemble

**Canaux disponibles** :
- 📧 **Email** : Rich HTML, templates
- 📱 **SMS** : Courts, urgents
- 🔔 **In-App** : Temps réel
- 📲 **Push** : Mobile (Phase 4)

**Événements déclencheurs** :
- Nouveau sondage disponible
- Rappel participation
- Résultats publiés
- Milestone atteint
- Date limite approche

### 4.2 Configuration Email

**Templates personnalisés** :
```html
Objet : 🎯 {{organization.name}} sollicite votre avis

Bonjour {{user.name}},

Un nouveau sondage vous concerne :
"{{poll.title}}"

{{poll.description}}

📍 Zone : {{poll.geographic_zone}}
⏰ Jusqu'au : {{poll.end_date}}
⏱️ Durée : {{poll.estimated_time}} min

[PARTICIPER MAINTENANT]

---
Se désinscrire : {{unsubscribe_link}}
```

**Bonnes pratiques email** :
- Objet court et incitatif
- Preheader informatif  
- CTA visible et unique
- Mobile-responsive
- Texte alternatif

### 4.3 Configuration SMS

**Format optimisé** :
```
CIVICPOLL: Nouveau sondage 
"{{poll.title}}" vous attend !
Répondez avant le {{date}}.
{{short_link}}
STOP au 36105
```

**Règles SMS** :
- 160 caractères max
- Lien court trackable
- Identification claire
- Opt-out obligatoire
- Heures envoi : 9h-20h

### 4.4 Stratégies Multi-Canal

**Séquence type** :
```
J+0 : Email invitation détaillée
J+3 : SMS rappel si non-ouvert
J+6 : Email relance personnalisée
J+7 : Notification in-app finale
```

**Personnalisation** :
- Canal préféré par user
- Fréquence adaptée
- Contenu contextualisé
- Tests A/B messaging

**Respect des préférences** :
```
Préférences de Marie :
☑️ Email : Immediat
☐ SMS : Jamais
☑️ In-App : Quotidien
Langue : Français
Timezone : Europe/Paris
```

### 4.5 Analytics Notifications

**Métriques suivies** :
```
Campaign "Budget Participatif"
├── Emails envoyés : 2,340
│   ├── Délivrés : 2,298 (98.2%)
│   ├── Ouverts : 1,104 (48.0%)
│   └── Cliqués : 567 (24.6%)
├── SMS envoyés : 890
│   ├── Délivrés : 878 (98.7%)
│   └── Cliqués : 234 (26.6%)
└── Conversion totale : 31.2%
```

**Optimisations** :
- Meilleurs horaires détectés
- Subject lines performantes
- Segments plus réactifs
- Canaux préférés

## 5. Exploitation Avancée des Données

### 5.1 Nouveaux Exports

**Excel Enrichi** 📊
- Onglets par question
- Graphiques intégrés
- Tableaux croisés
- Mise en forme auto

**Power BI Dataset** 💼
- Modèle de données prêt
- Mesures calculées
- Relations définies
- Refresh schedulé

**API JSON** 🔧
- Structure flexible
- Métadonnées complètes
- Pagination efficace
- Filtres avancés

### 5.2 Analytics par Type de Question

**Questions Likert** :
```
Satisfaction Globale (Likert 5)
├── Moyenne : 4.2/5 (↑0.3 vs N-1)
├── Médiane : 4
├── Mode : 5 (42% réponses)
├── Écart-type : 0.8
└── Distribution :
    5: ████████ 42%
    4: ██████ 31%
    3: ███ 17%
    2: █ 7%
    1: ▌ 3%
```

**Questions Géolocalisées** :
- Heatmap des signalements
- Clusters automatiques
- Distance moyenne
- Zones critiques

**Questions Médias** :
- Galerie photos/vidéos
- Tags automatiques (IA)
- Sentiments détectés
- Word cloud des thèmes

### 5.3 Rapports Automatisés

**Configuration** :
1. **Analytics → Rapports programmés**
2. Fréquence : Hebdo/Mensuel
3. Destinataires : Équipe + élus
4. Sections : KPIs + Insights

**Exemple rapport mensuel** :
```
RAPPORT CIVICPOLL - FÉVRIER 2024

RÉSUMÉ EXÉCUTIF
✓ 5 sondages menés
✓ 3,456 participations (+23%)
✓ Taux satisfaction : 82%

INSIGHTS CLÉS
1. Transport : Préoccupation #1
2. Zone Sud : Participation faible
3. 25-34 ans : Plus engagés

RECOMMANDATIONS
- Focus sur mobilité douce
- Campagne ciblée Zone Sud
- Maintenir momentum jeunes

[VOIR RAPPORT COMPLET]
```

## 6. Cas d'Usage Avancés Phase 2

### 6.1 Consultation Continue Multi-Phase

**Projet : Réaménagement Centre-Ville**

**Phase 1** : Diagnostic (Template)
- Photos problèmes actuels
- Géoloc points noirs
- Priorités classées

**Phase 2** : Co-conception (API)
- Propositions visuelles
- Vote sur options
- Budget allocation

**Phase 3** : Validation (Notifications)
- Plans finaux
- Derniers ajustements
- Engagement citoyen

**Résultat** : 2,000+ participants impliqués

### 6.2 Baromètre RH Automatisé

**Setup** :
- Template eNPS mensuel
- API déclenchement auto
- Segments par service
- Dashboard temps réel

**Process** :
```
1er lundi du mois → API crée sondage
Email personnalisé → 90% ouverture
Rappel J+3 SMS → +15% participation  
Rapport auto J+7 → Direction informée
```

**Impact** : Issues détectées 3x plus vite

### 6.3 Événement Hybride Interactif

**Conférence Annuelle**

**Pré-event** : 
- Inscription avec préférences
- Questions aux speakers
- Networking matching

**Live** :
- Polls temps réel
- Q&A modéré
- Feedback sessions

**Post-event** :
- Satisfaction détaillée
- Photos souvenirs
- Suggestions 2025

**Innovation** : Widget embedded stream

## 7. Optimisation de l'Engagement

### 7.1 Mix de Questions Optimal

**Structure gagnante** :
```
1. Accroche simple (choix)
2. Opinion rapide (étoiles)
3. Approfondissement (Likert)
4. Créativité (texte/média)
5. Conclusion positive (NPS)
```

**Durée idéale** : 5-7 minutes
**Questions max** : 10-12
**Médias** : 1-2 maximum

### 7.2 Templates + Personnalisation

**Approche** :
1. Partir d'un template éprouvé
2. Adapter 20-30% questions
3. Ajouter touche locale
4. Tester sur échantillon

**Exemple** :
```
Template "Satisfaction Service" générique
+ Question géoloc "Où avez-vous attendu ?"
+ Photo "Montrez-nous le problème"
= Sondage sur mesure performant
```

### 7.3 Multi-Canal Intelligent

**Segmentation** :
```
Jeunes (18-35) : SMS + In-App
Actifs (35-55) : Email + SMS
Seniors (55+) : Email uniquement
```

**Timing** :
```
Lundi 8h : Email B2B
Mardi 12h : SMS rappel
Jeudi 19h : Grand public
Dimanche : Éviter
```

## 8. Mesure du ROI Phase 2

### 8.1 Métriques d'Adoption

**Avant Phase 2** :
- 5 types questions basiques
- 0 templates
- Emails manuels
- Export CSV seul

**Après Phase 2** :
- 20 types utilisés
- 15 sondages/mois depuis templates
- 85% notifications automatiques
- 12 intégrations API actives

### 8.2 Gains Quantifiés

**Productivité** :
- -70% temps création sondage
- -90% temps envoi notifications
- x3 sondages menés/mois

**Qualité données** :
- +45% taux complétion
- +200% réponses détaillées
- +67% photos/médias reçus

**Engagement** :
- +23% taux participation
- +56% participants réguliers
- -40% abandons en cours

### 8.3 Valeur Créée

```
ROI Calculé :
├── Temps gagné : 40h/mois
├── Insights supplémentaires : x5
├── Décisions accélérées : 2 semaines
├── Satisfaction citoyens : +18 pts
└── ROI global : 340%
```

## 9. Pièges à Éviter

### 9.1 Surcharge de Fonctionnalités

❌ **Mauvais** : Utiliser 15 types différents
✅ **Bon** : 5-6 types cohérents

❌ **Mauvais** : Questions médias partout
✅ **Bon** : 1-2 médias si pertinent

### 9.2 Sur-Communication  

❌ **Mauvais** : Email + SMS + In-App simultanés
✅ **Bon** : Séquence progressive adaptée

❌ **Mauvais** : Rappels quotidiens
✅ **Bon** : 2-3 touchpoints maximum

### 9.3 API Sans Stratégie

❌ **Mauvais** : API pour tout automatiser
✅ **Bon** : API pour cas précis ROI clair

❌ **Mauvais** : Données sensibles via API
✅ **Bon** : Minimum nécessaire, sécurisé

## 10. Support et Ressources

### 10.1 Documentation

**Centre d'aide enrichi** :
- Guide par type de question
- Vidéos templates
- Exemples API code
- Best practices notifications

**Nouveaux contenus** :
- Webinar mensuel Phase 2
- Études de cas clients
- Cookbook intégrations
- Forum communauté

### 10.2 Support Technique

**API Support** :
- Documentation interactive
- Sandbox test
- Debug tools
- Office hours dev

**Template Support** :
- Conseil personnalisation
- Review avant lancement
- Optimisation continue

### 10.3 Formation

**Sessions disponibles** :
- "Maîtriser les 20 types"
- "Templates sur mesure"
- "API pour non-devs"
- "Notifications efficaces"

## 11. Préparer Phase 3

### 11.1 Gamification Coming Soon

**Ce qui arrive** :
- Points et niveaux
- Badge...

### 11.2 Commencer à Imaginer

- Quels badges pour vos participants ?
- Quels défis proposer ?
- Comment récompenser ?

## 12. Checklist Organisation Phase 2

### Configuration ✓
- [ ] Explorer les 20 types
- [ ] Tester 5+ templates
- [ ] Obtenir clé API
- [ ] Configurer notifications
- [ ] Activer webhooks

### Utilisation ✓
- [ ] 1er sondage enrichi
- [ ] 1er template personnalisé
- [ ] 1ère intégration API
- [ ] 1ère campagne multi-canal

### Optimisation ✓
- [ ] Analytics exploités
- [ ] Templates sauvegardés
- [ ] Workflows automatisés
- [ ] ROI mesuré

### Formation ✓
- [ ] Équipe formée
- [ ] Docs partagées
- [ ] Process définis
- [ ] Support identifié

---

## 🚀 Votre Potentiel Phase 2

Avec ces nouveaux outils :
- **Créez** des sondages 3x plus riches
- **Automatisez** 80% des tâches répétitives  
- **Intégrez** CivicPoll à votre écosystème
- **Communiquez** efficacement multi-canal
- **Exploitez** des insights actionnables

La Phase 2 transforme CivicPoll d'un simple outil de sondage en une plateforme complète d'engagement et d'analyse.

---

*Guide Administrateur Organisation - Phase 2 - Version 1.0*
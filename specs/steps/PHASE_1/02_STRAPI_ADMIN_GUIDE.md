# Phase 1 - Guide Administrateur Strapi : Configuration du MVP

## Introduction
Ce guide détaille la configuration et la gestion du MVP CivicPoll dans Strapi, incluant la gestion des zones géographiques, des sondages et des utilisateurs.

## 1. Gestion des Zones Géographiques

### 1.1 Accès au Module Géographique
1. Dans le menu latéral, cliquez sur **Content Manager**
2. Sélectionnez **Geographic Zones**

### 1.2 Structure Hiérarchique des Zones

La France est organisée en hiérarchie :
```
France (country)
├── Île-de-France (region)
│   ├── Paris - 75 (department)
│   │   ├── Paris (city)
│   │   └── ...
│   ├── Hauts-de-Seine - 92 (department)
│   │   ├── Boulogne-Billancourt (city)
│   │   └── ...
│   └── ...
├── Provence-Alpes-Côte d'Azur (region)
│   └── ...
└── ...
```

### 1.3 Vérification des Données Importées

1. **Vérifier le nombre total** :
   - 1 pays (France)
   - 18 régions
   - 101 départements
   - ~35,000 communes

2. **Rechercher une zone** :
   - Utilisez la barre de recherche
   - Filtrez par type (region, department, city)
   - Exemple : Cherchez "Paris" → Plusieurs résultats (région, département, ville)

### 1.4 Ajouter une Zone Manquante

Si une zone est manquante :
1. Cliquez sur **"+ Add new entry"**
2. Remplissez :
   - **Name** : Nom officiel
   - **Code** : Code INSEE ou postal
   - **Type** : Sélectionnez le niveau
   - **Parent** : Zone parente (obligatoire sauf pour le pays)
   - **Population** : Nombre d'habitants
3. Sauvegardez

## 2. Configuration des Templates de Sondages

### 2.1 Accès aux Templates
**Content Manager → Poll Templates**

### 2.2 Templates Pré-configurés

#### Template "Satisfaction"
- **Catégorie** : satisfaction
- **Questions types** :
  - Échelle de satisfaction (1-5)
  - Points positifs (texte)
  - Points à améliorer (texte)
  - Recommandation (0-10)

#### Template "Opinion"
- **Catégorie** : opinion
- **Questions types** :
  - Accord/Désaccord (échelle)
  - Priorités (classement)
  - Suggestions (texte libre)

#### Template "Vote"
- **Catégorie** : vote
- **Questions types** :
  - Choix unique
  - Second choix (optionnel)
  - Motivations (multiple)

### 2.3 Créer un Nouveau Template

1. **"+ Add new entry"**
2. **Informations de base** :
   - Name : Nom descriptif
   - Category : Choisir ou créer
   - Description : Usage recommandé
   - Icon : Uploader une icône

3. **Structure des questions** (JSON) :
```json
{
  "questions": [
    {
      "text": "Votre satisfaction globale ?",
      "type": "rating",
      "required": true,
      "options": {
        "min": 1,
        "max": 5,
        "labels": ["Très insatisfait", "Insatisfait", "Neutre", "Satisfait", "Très satisfait"]
      }
    },
    {
      "text": "Recommanderiez-vous ce service ?",
      "type": "single_choice",
      "required": true,
      "options": ["Oui", "Non", "Peut-être"]
    }
  ]
}
```

## 3. Gestion des Organisations

### 3.1 Organisation SMATFLOW
**Content Manager → Organizations**

L'organisation SMATFLOW est pré-créée :
- **Name** : SMATFLOW
- **Type** : Platform Owner
- **Settings** : Accès illimité

### 3.2 Paramètres de l'Organisation

1. **Quotas** (Phase 1 - illimités pour SMATFLOW) :
   - Sondages actifs : Illimité
   - Réponses/mois : Illimité
   - Zones géographiques : Toutes

2. **Branding** :
   - Logo : Upload du logo SMATFLOW
   - Couleurs : #1E3A8A (primaire), #3B82F6 (secondaire)
   - Email footer : Personnalisé

## 4. Gestion des Utilisateurs et Localisations

### 4.1 Validation des Localisations
**Content Manager → User Locations**

Surveillez les nouvelles inscriptions :
1. **Filtrer** : `verified = false`
2. Pour chaque entrée :
   - Vérifiez la cohérence (code postal ↔ ville)
   - Cliquez sur l'entrée
   - Si valide : Cochez **verified** ✓
   - Si invalide : Contactez l'utilisateur

### 4.2 Statistiques Utilisateurs

**Dashboard personnalisé** (Widget à ajouter) :
```
Utilisateurs totaux : 1,234
├── Avec localisation validée : 1,180 (95.6%)
├── En attente de validation : 54 (4.4%)
└── Par région :
    ├── Île-de-France : 456 (37%)
    ├── Auvergne-Rhône-Alpes : 234 (19%)
    └── ...
```

## 5. Création et Gestion des Sondages

### 5.1 Créer un Sondage
**Content Manager → Polls → "+ Add new entry"**

#### Étape 1 : Informations de Base
- **Title** : Titre accrocheur (max 200 caractères)
- **Description** : Contexte détaillé (rich text)
- **Slug** : URL automatique ou personnalisée
- **Status** : 
  - `draft` : En préparation
  - `active` : En ligne
  - `closed` : Terminé

#### Étape 2 : Ciblage Géographique (OBLIGATOIRE)
- **Geographic zones** : 
  - Cliquez sur **"+ Add relation"**
  - Recherchez et sélectionnez les zones
  - Exemples :
    - National : Sélectionnez "France"
    - Régional : "Île-de-France"
    - Local : "Paris" (ville)
  - **Minimum 1 zone requise**

#### Étape 3 : Période
- **Start date** : Date/heure de début
- **End date** : Date/heure de fin
- Fuseau horaire : Europe/Paris

#### Étape 4 : Paramètres
```json
{
  "allow_anonymous": false,
  "show_results": true,
  "randomize_questions": false,
  "one_response_per_user": true
}
```

### 5.2 Ajouter des Questions

Après avoir créé le sondage :
1. Allez dans **Content Manager → Questions**
2. **"+ Add new entry"**
3. Remplissez :
   - **Text** : La question
   - **Type** : single_choice, multiple_choice, text, rating
   - **Poll** : Sélectionnez votre sondage
   - **Order** : Position (1, 2, 3...)
   - **Required** : Obligatoire ou non

4. Pour les questions à choix, ajoutez les **Options**

### 5.3 Prévisualisation

Avant de publier :
1. Gardez le statut en **draft**
2. Utilisez l'URL de preview : `/admin/preview/polls/[id]`
3. Testez sur mobile et desktop
4. Vérifiez les zones géographiques

### 5.4 Publication

1. Changez le status à **active**
2. Sauvegardez
3. Le sondage est immédiatement disponible
4. Les notifications partent automatiquement

## 6. Suivi des Sondages en Temps Réel

### 6.1 Tableau de Bord Analytics
**Analytics → Poll Analytics**

Pour chaque sondage actif :
- **Participation** : 234/1,200 (19.5%)
- **Temps moyen** : 3min 45s
- **Taux de complétion** : 87%
- **Distribution géographique** : Carte

### 6.2 Réponses en Direct
**Content Manager → Response Sessions**

Filtrez par sondage pour voir :
- Nouvelles participations
- Heures de pointe
- Zones les plus actives

### 6.3 Exporter les Données

1. Sélectionnez le sondage
2. **Actions → Export**
3. Formats disponibles :
   - CSV : Pour Excel
   - JSON : Pour analyse
   - PDF : Rapport formaté

## 7. Gestion des Notifications

### 7.1 Notifications Automatiques
Configurées automatiquement pour :
- Nouveau sondage dans la zone
- Rappel 24h avant la fin
- Remerciement post-participation

### 7.2 Logs des Emails
**Email → Sent Emails**

Vérifiez :
- Statut d'envoi
- Taux d'ouverture
- Bounces/Erreurs

### 7.3 Désactiver les Notifications

Pour un utilisateur spécifique :
1. **Users → [Utilisateur]**
2. Onglet **Preferences**
3. Décochez les notifications

## 8. Modération et Conformité

### 8.1 Modération des Réponses Texte

**Content Manager → Text Responses**

Filtrez par :
- `flagged = true` : Contenu signalé
- `reviewed = false` : Non vérifié

Actions :
- ✅ Approuver
- 🚫 Masquer
- ⚠️ Signaler pour review

### 8.2 Exports RGPD

Quand un utilisateur demande ses données :
1. **Users → [Utilisateur]**
2. **Actions → Export GDPR Data**
3. Un ZIP est généré avec :
   - Profil utilisateur
   - Localisations
   - Participations
   - Réponses (anonymisées)

### 8.3 Suppression RGPD

1. **Users → [Utilisateur]**
2. **Actions → Anonymize & Delete**
3. Confirmation requise
4. Les réponses restent mais sont anonymisées

## 9. Rapports et Statistiques

### 9.1 Rapport Hebdomadaire

Chaque lundi, générez :
1. **Reports → Weekly Stats**
2. Contient :
   - Nouveaux utilisateurs
   - Sondages créés/terminés
   - Taux de participation
   - Zones les plus actives

### 9.2 Analyse Géographique

**Analytics → Geographic Insights**

Visualisez :
- Heatmap de participation
- Zones sous-représentées
- Comparaisons régionales

## 10. Résolution de Problèmes

### 10.1 Utilisateur ne peut pas voter

Vérifiez :
1. **Localisation validée ?** → User Locations
2. **Dans la bonne zone ?** → Geographic match
3. **Déjà voté ?** → Response Sessions
4. **Sondage actif ?** → Poll status

### 10.2 Notifications non reçues

1. **Email → Logs**
2. Cherchez l'email de l'utilisateur
3. Vérifiez :
   - Bounce ?
   - Spam ?
   - Adresse valide ?

### 10.3 Performance lente

1. **System → Health Check**
2. Vérifiez :
   - CPU/RAM usage
   - Database queries
   - Cache hit rate
3. Actions :
   - Clear cache
   - Optimize queries
   - Scale if needed

## 11. Bonnes Pratiques Phase 1

### Pour les Sondages
- ✅ Toujours définir une zone géographique
- ✅ Limiter à 10 questions maximum
- ✅ Tester avant de publier
- ✅ Durée raisonnable (1-2 semaines)

### Pour la Modération
- ✅ Vérifier quotidiennement
- ✅ Répondre aux signalements sous 24h
- ✅ Documenter les décisions

### Pour la Sécurité
- ✅ Auditer les accès mensuellement
- ✅ Vérifier les logs d'erreur
- ✅ Maintenir les backups à jour

## 12. Checklist Quotidienne Admin

### Matin (9h)
- [ ] Vérifier les nouveaux utilisateurs
- [ ] Valider les localisations en attente
- [ ] Checker les sondages qui démarrent

### Midi (12h)
- [ ] Surveiller les métriques temps réel
- [ ] Répondre aux alertes éventuelles

### Soir (17h)
- [ ] Exporter les stats du jour
- [ ] Modérer les contenus signalés
- [ ] Préparer les sondages du lendemain

## Support et Escalade

### Niveau 1 - Admin Strapi
- Gestion quotidienne
- Modération basique
- Support utilisateurs

### Niveau 2 - Admin Système
- Problèmes techniques
- Performance
- Sécurité

### Niveau 3 - Développement
- Bugs
- Nouvelles fonctionnalités
- Intégrations

---

## ✅ Validation Finale Configuration Phase 1

- [ ] Zones géographiques complètes et vérifiées
- [ ] 3 templates de base créés et testés
- [ ] Organisation SMATFLOW configurée
- [ ] Au moins 1 sondage test créé
- [ ] Notifications email testées
- [ ] Export RGPD fonctionnel
- [ ] Documentation à jour

**Une fois validé, la Phase 1 est opérationnelle !**

---

*Guide Administrateur Strapi - Phase 1 - Version 1.0*
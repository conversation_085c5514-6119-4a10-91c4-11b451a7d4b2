# Phase 1 - Implémentation Technique : MVP Géolocalisé

## Vue d'ensemble
Cette phase implémente le MVP de CivicPoll avec système de géolocalisation obligatoire, intégration SSO SMATFLOW et premiers sondages fonctionnels.

## 1. Content-Types Strapi

### 1.1 GeographicZone (Zones Géographiques)
```javascript
// api/geographic-zone/content-types/geographic-zone/schema.json
{
  "kind": "collectionType",
  "collectionName": "geographic_zones",
  "info": {
    "singularName": "geographic-zone",
    "pluralName": "geographic-zones",
    "displayName": "Zone Géographique"
  },
  "options": {
    "draftAndPublish": false
  },
  "attributes": {
    "name": {
      "type": "string",
      "required": true,
      "unique": true
    },
    "code": {
      "type": "string",
      "required": true,
      "unique": true
    },
    "type": {
      "type": "enumeration",
      "enum": ["country", "region", "department", "city", "district"],
      "required": true
    },
    "parent": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::geographic-zone.geographic-zone"
    },
    "children": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::geographic-zone.geographic-zone",
      "mappedBy": "parent"
    },
    "geometry": {
      "type": "json",
      "description": "GeoJSON polygon"
    },
    "population": {
      "type": "integer"
    },
    "insee_code": {
      "type": "string"
    }
  }
}
```

### 1.2 UserLocation (Localisation Utilisateur)
```javascript
// api/user-location/content-types/user-location/schema.json
{
  "kind": "collectionType",
  "collectionName": "user_locations",
  "info": {
    "singularName": "user-location",
    "pluralName": "user-locations",
    "displayName": "Localisation Utilisateur"
  },
  "attributes": {
    "user": {
      "type": "relation",
      "relation": "oneToOne",
      "target": "plugin::users-permissions.user"
    },
    "address": {
      "type": "string",
      "required": true
    },
    "postal_code": {
      "type": "string",
      "required": true,
      "regex": "^[0-9]{5}$"
    },
    "city": {
      "type": "string",
      "required": true
    },
    "geographic_zone": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::geographic-zone.geographic-zone",
      "required": true
    },
    "coordinates": {
      "type": "json",
      "description": "{lat: number, lng: number}"
    },
    "verified": {
      "type": "boolean",
      "default": false
    },
    "verified_at": {
      "type": "datetime"
    }
  }
}
```

### 1.3 Poll (Sondage)
```javascript
// api/poll/content-types/poll/schema.json
{
  "kind": "collectionType",
  "collectionName": "polls",
  "info": {
    "singularName": "poll",
    "pluralName": "polls",
    "displayName": "Sondage"
  },
  "options": {
    "draftAndPublish": true
  },
  "attributes": {
    "title": {
      "type": "string",
      "required": true,
      "maxLength": 200
    },
    "description": {
      "type": "richtext",
      "required": true
    },
    "slug": {
      "type": "uid",
      "targetField": "title",
      "required": true
    },
    "status": {
      "type": "enumeration",
      "enum": ["draft", "active", "closed", "archived"],
      "default": "draft"
    },
    "geographic_zones": {
      "type": "relation",
      "relation": "manyToMany",
      "target": "api::geographic-zone.geographic-zone",
      "required": true,
      "min": 1
    },
    "start_date": {
      "type": "datetime",
      "required": true
    },
    "end_date": {
      "type": "datetime",
      "required": true
    },
    "created_by": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "plugin::users-permissions.user"
    },
    "organization": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::organization.organization"
    },
    "questions": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::question.question",
      "mappedBy": "poll"
    },
    "template": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::poll-template.poll-template"
    },
    "settings": {
      "type": "json",
      "default": {
        "allow_anonymous": false,
        "show_results": true,
        "randomize_questions": false,
        "one_response_per_user": true
      }
    },
    "analytics": {
      "type": "component",
      "repeatable": false,
      "component": "analytics.poll-analytics"
    }
  }
}
```

### 1.4 Question
```javascript
// api/question/content-types/question/schema.json
{
  "kind": "collectionType",
  "collectionName": "questions",
  "info": {
    "singularName": "question",
    "pluralName": "questions",
    "displayName": "Question"
  },
  "attributes": {
    "text": {
      "type": "string",
      "required": true
    },
    "type": {
      "type": "enumeration",
      "enum": ["single_choice", "multiple_choice", "text", "rating", "ranking"],
      "required": true
    },
    "required": {
      "type": "boolean",
      "default": true
    },
    "order": {
      "type": "integer",
      "required": true,
      "min": 1
    },
    "poll": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::poll.poll",
      "inversedBy": "questions"
    },
    "options": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::option.option",
      "mappedBy": "question"
    },
    "validation": {
      "type": "json",
      "description": "Type-specific validation rules"
    }
  }
}
```

### 1.5 Templates de Sondages
```javascript
// api/poll-template/content-types/poll-template/schema.json
{
  "kind": "collectionType",
  "collectionName": "poll_templates",
  "info": {
    "singularName": "poll-template",
    "pluralName": "poll-templates",
    "displayName": "Template Sondage"
  },
  "attributes": {
    "name": {
      "type": "string",
      "required": true
    },
    "category": {
      "type": "enumeration",
      "enum": ["satisfaction", "opinion", "vote", "evaluation", "custom"],
      "required": true
    },
    "description": {
      "type": "text"
    },
    "questions_template": {
      "type": "json",
      "required": true
    },
    "icon": {
      "type": "media",
      "allowedTypes": ["images"]
    }
  }
}
```

## 2. Plugin SSO SMATFLOW

### 2.1 Installation et Configuration
```bash
cd /opt/civicpoll/app
npm install strapi-plugin-sso
```

### 2.2 Configuration du Plugin
```javascript
// config/plugins.js
module.exports = ({ env }) => ({
  'sso': {
    enabled: true,
    config: {
      provider: 'oauth2',
      providerOptions: {
        authorizationURL: env('SSO_AUTH_URL', 'https://sso.smatflow.xyz/oauth/authorize'),
        tokenURL: env('SSO_TOKEN_URL', 'https://sso.smatflow.xyz/oauth/token'),
        clientID: env('SSO_CLIENT_ID'),
        clientSecret: env('SSO_CLIENT_SECRET'),
        callbackURL: env('SSO_CALLBACK_URL', 'https://civicpoll.fr.smatflow.xyz/api/auth/sso/callback'),
        scope: ['profile', 'email', 'location']
      },
      autoRegister: true,
      autoLogin: true,
      requiredFields: ['email', 'postal_code', 'city']
    }
  }
});
```

### 2.3 Middleware de Validation Géographique
```javascript
// src/middlewares/geographic-validation.js
module.exports = (config, { strapi }) => {
  return async (ctx, next) => {
    if (ctx.request.url.startsWith('/api/polls/') && ctx.request.method === 'POST') {
      const pollId = ctx.params.id;
      const userId = ctx.state.user?.id;
      
      if (!userId) {
        return ctx.unauthorized('Authentication required');
      }
      
      // Récupérer la localisation de l'utilisateur
      const userLocation = await strapi.entityService.findOne('api::user-location.user-location', {
        filters: { user: userId },
        populate: ['geographic_zone']
      });
      
      if (!userLocation || !userLocation.verified) {
        return ctx.badRequest('User location not verified');
      }
      
      // Récupérer les zones du sondage
      const poll = await strapi.entityService.findOne('api::poll.poll', pollId, {
        populate: ['geographic_zones']
      });
      
      // Vérifier que l'utilisateur est dans une zone autorisée
      const userZoneId = userLocation.geographic_zone.id;
      const allowedZoneIds = poll.geographic_zones.map(z => z.id);
      
      // Vérifier aussi les zones parentes (département -> région -> pays)
      const isAllowed = await checkZoneHierarchy(userZoneId, allowedZoneIds);
      
      if (!isAllowed) {
        return ctx.forbidden('Ce sondage n\'est pas disponible dans votre zone');
      }
    }
    
    await next();
  };
};

async function checkZoneHierarchy(userZoneId, allowedZoneIds) {
  // Logique de vérification hiérarchique
  // Une zone enfant est autorisée si sa zone parente l'est
}
```

## 3. Import des Données Géographiques

### 3.1 Script d'Import
```javascript
// scripts/import-geographic-data.js
const fs = require('fs');
const path = require('path');

async function importGeographicData(strapi) {
  console.log('🌍 Import des données géographiques France...');
  
  // Import depuis les données INSEE/IGN
  const geoData = JSON.parse(
    fs.readFileSync(path.join(__dirname, '../data/france-geo.json'))
  );
  
  // 1. Créer le pays
  const france = await strapi.entityService.create('api::geographic-zone.geographic-zone', {
    data: {
      name: 'France',
      code: 'FR',
      type: 'country',
      population: 67000000
    }
  });
  
  // 2. Importer les régions
  for (const region of geoData.regions) {
    const regionEntity = await strapi.entityService.create('api::geographic-zone.geographic-zone', {
      data: {
        name: region.name,
        code: region.code,
        type: 'region',
        parent: france.id,
        geometry: region.geometry,
        population: region.population
      }
    });
    
    // 3. Importer les départements de la région
    for (const dept of region.departments) {
      const deptEntity = await strapi.entityService.create('api::geographic-zone.geographic-zone', {
        data: {
          name: dept.name,
          code: dept.code,
          type: 'department',
          parent: regionEntity.id,
          geometry: dept.geometry,
          population: dept.population
        }
      });
      
      // 4. Importer les principales villes
      for (const city of dept.cities) {
        await strapi.entityService.create('api::geographic-zone.geographic-zone', {
          data: {
            name: city.name,
            code: city.postal_code,
            type: 'city',
            parent: deptEntity.id,
            geometry: city.geometry,
            population: city.population,
            insee_code: city.insee_code
          }
        });
      }
    }
  }
  
  console.log('✅ Import terminé');
}

module.exports = { importGeographicData };
```

### 3.2 API de Géocodage
```javascript
// src/api/geocoding/services/geocoding.js
const axios = require('axios');

module.exports = {
  async validateAddress(address, postalCode, city) {
    try {
      // Utilisation de l'API Adresse du gouvernement français
      const response = await axios.get('https://api-adresse.data.gouv.fr/search/', {
        params: {
          q: `${address} ${postalCode} ${city}`,
          postcode: postalCode,
          city: city,
          limit: 1
        }
      });
      
      if (response.data.features.length === 0) {
        throw new Error('Address not found');
      }
      
      const feature = response.data.features[0];
      const coordinates = feature.geometry.coordinates;
      const properties = feature.properties;
      
      // Trouver la zone géographique correspondante
      const zone = await strapi.db.query('api::geographic-zone.geographic-zone').findOne({
        where: {
          type: 'city',
          insee_code: properties.citycode
        }
      });
      
      return {
        valid: true,
        coordinates: {
          lat: coordinates[1],
          lng: coordinates[0]
        },
        normalized_address: properties.label,
        city: properties.city,
        postal_code: properties.postcode,
        geographic_zone: zone
      };
    } catch (error) {
      return {
        valid: false,
        error: 'Invalid address'
      };
    }
  }
};
```

## 4. Frontend React - Interfaces

### 4.1 Configuration React App
```javascript
// frontend/src/config/api.js
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://civicpoll.fr.smatflow.xyz/api';
export const SSO_LOGIN_URL = `${API_BASE_URL}/auth/sso`;

// frontend/src/services/auth.js
import { API_BASE_URL, SSO_LOGIN_URL } from '../config/api';

export const authService = {
  login() {
    window.location.href = SSO_LOGIN_URL;
  },
  
  async getCurrentUser() {
    const response = await fetch(`${API_BASE_URL}/users/me`, {
      credentials: 'include'
    });
    return response.json();
  },
  
  async validateLocation(postalCode, city) {
    const response = await fetch(`${API_BASE_URL}/user-locations/validate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ postal_code: postalCode, city }),
      credentials: 'include'
    });
    return response.json();
  }
};
```

### 4.2 Composant de Validation d'Adresse
```jsx
// frontend/src/components/AddressValidation.jsx
import React, { useState } from 'react';
import { MapContainer, TileLayer, Marker } from 'react-leaflet';

export function AddressValidation({ onValidated }) {
  const [formData, setFormData] = useState({
    address: '',
    postalCode: '',
    city: ''
  });
  const [validation, setValidation] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const result = await authService.validateLocation(
        formData.postalCode,
        formData.city
      );
      
      if (result.valid) {
        setValidation(result);
      } else {
        alert('Adresse non reconnue. Veuillez vérifier.');
      }
    } catch (error) {
      console.error('Validation error:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const confirmLocation = async () => {
    await authService.updateUserLocation(validation);
    onValidated(validation.geographic_zone);
  };
  
  return (
    <div className="address-validation">
      <h2>Confirmez votre localisation</h2>
      <p>Pour accéder aux sondages de votre zone, nous devons vérifier votre adresse.</p>
      
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          placeholder="Adresse (optionnel)"
          value={formData.address}
          onChange={(e) => setFormData({...formData, address: e.target.value})}
        />
        
        <input
          type="text"
          placeholder="Code postal"
          pattern="[0-9]{5}"
          required
          value={formData.postalCode}
          onChange={(e) => setFormData({...formData, postalCode: e.target.value})}
        />
        
        <input
          type="text"
          placeholder="Ville"
          required
          value={formData.city}
          onChange={(e) => setFormData({...formData, city: e.target.value})}
        />
        
        <button type="submit" disabled={loading}>
          {loading ? 'Vérification...' : 'Valider'}
        </button>
      </form>
      
      {validation && (
        <div className="validation-result">
          <h3>Adresse détectée :</h3>
          <p>{validation.normalized_address}</p>
          <p>Zone : {validation.geographic_zone.name}</p>
          
          <MapContainer
            center={[validation.coordinates.lat, validation.coordinates.lng]}
            zoom={13}
            style={{ height: '300px', width: '100%' }}
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <Marker position={[validation.coordinates.lat, validation.coordinates.lng]} />
          </MapContainer>
          
          <button onClick={confirmLocation} className="btn-primary">
            Confirmer cette adresse
          </button>
        </div>
      )}
    </div>
  );
}
```

### 4.3 Dashboard Organisation - Création de Sondage
```jsx
// frontend/src/components/PollCreator.jsx
import React, { useState, useEffect } from 'react';
import { GeographicZoneSelector } from './GeographicZoneSelector';
import { QuestionBuilder } from './QuestionBuilder';
import { TemplateSelector } from './TemplateSelector';

export function PollCreator() {
  const [poll, setPoll] = useState({
    title: '',
    description: '',
    geographic_zones: [],
    questions: [],
    start_date: '',
    end_date: '',
    template: null
  });
  const [templates, setTemplates] = useState([]);
  
  useEffect(() => {
    loadTemplates();
  }, []);
  
  const loadTemplates = async () => {
    const response = await fetch(`${API_BASE_URL}/poll-templates`);
    const data = await response.json();
    setTemplates(data.data);
  };
  
  const handleTemplateSelect = (template) => {
    setPoll({
      ...poll,
      template: template.id,
      questions: template.attributes.questions_template
    });
  };
  
  const handleSubmit = async () => {
    // Validation
    if (poll.geographic_zones.length === 0) {
      alert('Vous devez sélectionner au moins une zone géographique');
      return;
    }
    
    if (poll.questions.length === 0) {
      alert('Vous devez ajouter au moins une question');
      return;
    }
    
    try {
      const response = await fetch(`${API_BASE_URL}/polls`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ data: poll }),
        credentials: 'include'
      });
      
      if (response.ok) {
        const created = await response.json();
        window.location.href = `/polls/${created.data.attributes.slug}`;
      }
    } catch (error) {
      console.error('Error creating poll:', error);
    }
  };
  
  return (
    <div className="poll-creator">
      <h1>Créer un nouveau sondage</h1>
      
      <section className="templates-section">
        <h2>Choisir un template (optionnel)</h2>
        <TemplateSelector
          templates={templates}
          onSelect={handleTemplateSelect}
        />
      </section>
      
      <section className="basic-info">
        <input
          type="text"
          placeholder="Titre du sondage"
          value={poll.title}
          onChange={(e) => setPoll({...poll, title: e.target.value})}
          required
        />
        
        <textarea
          placeholder="Description"
          value={poll.description}
          onChange={(e) => setPoll({...poll, description: e.target.value})}
          required
        />
      </section>
      
      <section className="geographic-targeting">
        <h2>Zone géographique (obligatoire)</h2>
        <GeographicZoneSelector
          selected={poll.geographic_zones}
          onChange={(zones) => setPoll({...poll, geographic_zones: zones})}
        />
      </section>
      
      <section className="questions-section">
        <h2>Questions</h2>
        <QuestionBuilder
          questions={poll.questions}
          onChange={(questions) => setPoll({...poll, questions})}
        />
      </section>
      
      <section className="schedule">
        <h2>Période</h2>
        <input
          type="datetime-local"
          value={poll.start_date}
          onChange={(e) => setPoll({...poll, start_date: e.target.value})}
          required
        />
        <input
          type="datetime-local"
          value={poll.end_date}
          onChange={(e) => setPoll({...poll, end_date: e.target.value})}
          required
        />
      </section>
      
      <button onClick={handleSubmit} className="btn-primary">
        Créer le sondage
      </button>
    </div>
  );
}
```

## 5. Analytics et Notifications

### 5.1 Service Analytics
```javascript
// src/api/analytics/services/analytics.js
module.exports = {
  async trackEvent(eventType, data) {
    await strapi.entityService.create('api::analytics-event.analytics-event', {
      data: {
        type: eventType,
        user: data.userId,
        poll: data.pollId,
        metadata: data.metadata,
        timestamp: new Date()
      }
    });
  },
  
  async calculatePollAnalytics(pollId) {
    const poll = await strapi.entityService.findOne('api::poll.poll', pollId, {
      populate: ['responses', 'geographic_zones']
    });
    
    const analytics = {
      total_participants: 0,
      completion_rate: 0,
      average_time: 0,
      geographic_distribution: {},
      question_analytics: []
    };
    
    // Calculs...
    
    return analytics;
  }
};
```

### 5.2 Plugin Notifications Email
```javascript
// config/plugins.js
module.exports = ({ env }) => ({
  email: {
    config: {
      provider: 'sendgrid',
      providerOptions: {
        apiKey: env('SENDGRID_API_KEY'),
      },
      settings: {
        defaultFrom: '<EMAIL>',
        defaultReplyTo: '<EMAIL>',
        testAddress: '<EMAIL>',
      },
    },
  },
});

// src/api/poll/lifecycles.js
module.exports = {
  async afterCreate(event) {
    const { result } = event;
    
    // Notifier les utilisateurs de la zone
    const users = await strapi.db.query('plugin::users-permissions.user').findMany({
      where: {
        user_location: {
          geographic_zone: {
            id: { $in: result.geographic_zones.map(z => z.id) }
          }
        }
      }
    });
    
    for (const user of users) {
      await strapi.plugins['email'].services.email.send({
        to: user.email,
        subject: `Nouveau sondage disponible : ${result.title}`,
        html: `
          <h2>Un nouveau sondage vous concerne !</h2>
          <p>${result.description}</p>
          <a href="https://civicpoll.fr.smatflow.xyz/polls/${result.slug}">
            Participer maintenant
          </a>
        `
      });
    }
  }
};
```

## 6. Tests d'Intégration

### 6.1 Tests API
```javascript
// tests/api/poll.test.js
const request = require('supertest');

describe('Poll API', () => {
  let authToken;
  let userId;
  
  beforeAll(async () => {
    // Login via SSO mock
    const loginResponse = await request(strapi.server)
      .post('/api/auth/local')
      .send({
        identifier: '<EMAIL>',
        password: 'testpass123'
      });
    
    authToken = loginResponse.body.jwt;
    userId = loginResponse.body.user.id;
  });
  
  test('Should require geographic zone when creating poll', async () => {
    const response = await request(strapi.server)
      .post('/api/polls')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        data: {
          title: 'Test Poll',
          description: 'Test',
          // Missing geographic_zones
        }
      });
    
    expect(response.status).toBe(400);
    expect(response.body.error.message).toContain('geographic_zones');
  });
  
  test('Should prevent participation outside zone', async () => {
    // Create poll for Paris only
    const poll = await strapi.entityService.create('api::poll.poll', {
      data: {
        title: 'Paris Only Poll',
        geographic_zones: ['paris-zone-id']
      }
    });
    
    // Try to respond from Lyon
    const response = await request(strapi.server)
      .post(`/api/polls/${poll.id}/responses`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        data: { answers: [] }
      });
    
    expect(response.status).toBe(403);
    expect(response.body.error.message).toContain('pas disponible dans votre zone');
  });
});
```

### 6.2 Tests E2E
```javascript
// tests/e2e/poll-participation.spec.js
describe('Poll Participation Flow', () => {
  it('should complete full participation flow', async () => {
    // 1. Login via SSO
    await page.goto('https://civicpoll.fr.smatflow.xyz');
    await page.click('[data-test="login-button"]');
    
    // 2. Validate address if needed
    if (await page.isVisible('[data-test="address-validation"]')) {
      await page.fill('[name="postal_code"]', '75001');
      await page.fill('[name="city"]', 'Paris');
      await page.click('[data-test="validate-address"]');
      await page.click('[data-test="confirm-address"]');
    }
    
    // 3. Access poll
    await page.click('[data-test="poll-card"]');
    
    // 4. Answer questions
    await page.click('[data-test="answer-option-1"]');
    await page.click('[data-test="next-question"]');
    
    // 5. Submit
    await page.click('[data-test="submit-poll"]');
    
    // 6. View results
    expect(await page.isVisible('[data-test="poll-results"]')).toBeTruthy();
  });
});
```

## 7. Déploiement et Migration

### 7.1 Script de Migration
```bash
#!/bin/bash
# deploy-phase1.sh

echo "🚀 Déploiement Phase 1..."

# 1. Backup de sécurité
./scripts/backup.sh

# 2. Pull latest code
cd /opt/civicpoll/app
git pull origin main

# 3. Install dependencies
npm ci --production

# 4. Run migrations
npm run strapi migrate

# 5. Import geographic data
node scripts/import-geographic-data.js

# 6. Build frontend
cd frontend
npm ci
npm run build

# 7. Restart services
pm2 restart civicpoll
pm2 restart civicpoll-worker

# 8. Health check
sleep 10
curl -f https://civicpoll.fr.smatflow.xyz/api/health || exit 1

echo "✅ Phase 1 déployée avec succès"
```

## 8. Monitoring Spécifique Phase 1

### 8.1 Métriques Clés
```javascript
// src/services/metrics.js
module.exports = {
  async collectMetrics() {
    const metrics = {
      users: {
        total: await strapi.db.query('plugin::users-permissions.user').count(),
        with_location: await strapi.db.query('api::user-location.user-location').count({
          where: { verified: true }
        })
      },
      polls: {
        active: await strapi.db.query('api::poll.poll').count({
          where: { status: 'active' }
        }),
        by_zone: await this.getPollsByZone()
      },
      participation: {
        total_responses: await strapi.db.query('api::response.response').count(),
        unique_participants: await this.getUniqueParticipants()
      }
    };
    
    return metrics;
  }
};
```

---

## ✅ VALIDATION FINALE DE LA PHASE 1

### Tests Fonctionnels
- [ ] SSO SMATFLOW fonctionnel
- [ ] Validation d'adresse opérationnelle
- [ ] Création de sondage avec zone obligatoire
- [ ] Restriction géographique effective
- [ ] 3 templates de base disponibles
- [ ] Analytics temps réel
- [ ] Notifications email

### Tests Techniques
- [ ] Tous les Content-Types créés
- [ ] Import données géographiques réussi
- [ ] Middleware géographique actif
- [ ] API de géocodage fonctionnelle
- [ ] Tests unitaires passent (> 80% coverage)
- [ ] Tests E2E passent

### Tests Performance
- [ ] Temps de réponse API < 200ms
- [ ] Géocodage < 500ms
- [ ] Chargement page < 2s

### Documentation
- [ ] API documentée (Swagger)
- [ ] Guide utilisateur à jour
- [ ] Changelog complété

**⚠️ NE PAS PASSER À LA PHASE 2 SANS VALIDATION COMPLÈTE**

---

*Document technique - Phase 1 - Version 1.0*
# CivicPoll - Development Implementation Plan (Strapi v5)

## 🎯 Project Context

**CivicPoll** is a geolocated survey platform that enables organizations to create location-specific polls with mandatory geographic targeting. The platform features SSO authentication, gamification mechanics, and comprehensive analytics. This development plan focuses purely on implementation details using Strapi CMS v5.

### Core Technical Requirements
- **Mandatory Geographic Scoping**: Every poll must target specific geographic zones
- **Multi-Instance Architecture**: One Strapi instance per country
- **Real-time Features**: Live results and gamification updates
- **GDPR Compliance**: Privacy-first design with full data control
- **API-First**: Complete REST API for third-party integrations

---

## 🛠️ Development Stack

### Core Technologies
- **Backend**: Strapi CMS v5.x (latest stable)
- **Frontend**: React 18+ with TypeScript
- **Database**: PostgreSQL 15+ with PostGIS 3.3+
- **Cache**: Redis 7+
- **Real-time**: Socket.IO 4.x
- **Runtime**: Node.js 20 LTS

### Key Libraries & Frameworks

**Backend (Strapi)**:
```json
{
  "@strapi/strapi": "^5.0.0",
  "@strapi/plugin-users-permissions": "^5.0.0",
  "@strapi/plugin-email": "^5.0.0",
  "@strapi/provider-email-sendgrid": "^5.0.0",
  "pg": "^8.11.0",
  "socket.io": "^4.7.0",
  "redis": "^4.6.0",
  "bull": "^4.11.0",
  "node-cron": "^3.0.0",
  "jsonwebtoken": "^9.0.0",
  "bcrypt": "^5.1.0"
}
```

**Frontend**:
```json
{
  "react": "^18.2.0",
  "typescript": "^5.3.0",
  "react-router-dom": "^6.20.0",
  "react-query": "^3.39.0",
  "axios": "^1.6.0",
  "react-leaflet": "^4.2.0",
  "leaflet": "^1.9.0",
  "framer-motion": "^10.16.0",
  "react-hook-form": "^7.48.0",
  "socket.io-client": "^4.7.0",
  "recharts": "^2.10.0"
}
```

---

## 📋 Phase 0: Foundation Development (Weeks 1-4)

### 1. Strapi v5 Project Setup

**Initial Configuration**:
```bash
# Create new Strapi v5 project
npx create-strapi@latest civicpoll-backend --typescript

# Project structure
civicpoll/
├── backend/           # Strapi v5 application
├── frontend/          # React application
├── shared/           # Shared TypeScript types
└── scripts/          # Development utilities
```

**Database Configuration** (`backend/config/database.ts`):
```typescript
export default ({ env }) => ({
  connection: {
    client: 'postgres',
    connection: {
      host: env('DATABASE_HOST', 'localhost'),
      port: env.int('DATABASE_PORT', 5432),
      database: env('DATABASE_NAME', 'civicpoll'),
      user: env('DATABASE_USERNAME', 'postgres'),
      password: env('DATABASE_PASSWORD', ''),
      ssl: env.bool('DATABASE_SSL', false),
    },
    pool: {
      min: 2,
      max: 10,
    },
    acquireConnectionTimeout: 60000,
  },
});
```

### 2. Core Security Implementation

**Custom Middleware** (`backend/src/middlewares/security.ts`):
```typescript
export default (config, { strapi }) => {
  return async (ctx, next) => {
    // Rate limiting
    const rateLimiter = strapi.service('api::security.rate-limiter');
    await rateLimiter.checkLimit(ctx);
    
    // Security headers
    ctx.set({
      'X-Frame-Options': 'SAMEORIGIN',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000',
    });
    
    await next();
  };
};
```

### 3. GDPR Plugin Development

**Plugin Structure**:
```
backend/src/plugins/gdpr/
├── admin/
├── server/
│   ├── controllers/
│   │   ├── user-data.ts
│   │   └── consent.ts
│   ├── services/
│   │   ├── anonymization.ts
│   │   ├── export.ts
│   │   └── audit.ts
│   ├── routes/
│   └── bootstrap.ts
└── package.json
```

**GDPR Service Implementation**:
```typescript
// backend/src/plugins/gdpr/server/services/export.ts
export default ({ strapi }) => ({
  async exportUserData(userId: string) {
    const user = await strapi.entityService.findOne(
      'plugin::users-permissions.user',
      userId,
      {
        populate: {
          responses: true,
          sessions: true,
          badges: true,
          location: true,
        },
      }
    );
    
    return this.formatForExport(user);
  },
  
  async anonymizeUser(userId: string) {
    const timestamp = Date.now();
    return strapi.entityService.update(
      'plugin::users-permissions.user',
      userId,
      {
        data: {
          email: `deleted_${timestamp}@anonymous.local`,
          username: `deleted_user_${timestamp}`,
          firstName: 'Deleted',
          lastName: 'User',
          // Keep responses but remove identifying info
        },
      }
    );
  },
});
```

### 4. Audit System

**Audit Content Type** (`backend/src/api/audit-log/content-types/audit-log/schema.json`):
```json
{
  "kind": "collectionType",
  "collectionName": "audit_logs",
  "info": {
    "singularName": "audit-log",
    "pluralName": "audit-logs",
    "displayName": "Audit Log"
  },
  "options": {
    "draftAndPublish": false
  },
  "attributes": {
    "action": {
      "type": "enumeration",
      "enum": ["create", "update", "delete", "login", "export", "consent"],
      "required": true
    },
    "entity": {
      "type": "string",
      "required": true
    },
    "entityId": {
      "type": "string"
    },
    "user": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "plugin::users-permissions.user"
    },
    "metadata": {
      "type": "json"
    },
    "ip": {
      "type": "string"
    },
    "userAgent": {
      "type": "string"
    }
  }
}
```

---

## 📋 Phase 1: Core Survey System (Weeks 5-12)

### 1. Geographic Data Model

**Geographic Zone Content Type**:
```typescript
// backend/src/api/geographic-zone/content-types/geographic-zone/schema.ts
export default {
  kind: 'collectionType',
  collectionName: 'geographic_zones',
  info: {
    singularName: 'geographic-zone',
    pluralName: 'geographic-zones',
    displayName: 'Geographic Zone',
  },
  options: {
    draftAndPublish: false,
  },
  attributes: {
    name: {
      type: 'string',
      required: true,
    },
    code: {
      type: 'string',
      required: true,
      unique: true,
    },
    type: {
      type: 'enumeration',
      enum: ['country', 'region', 'department', 'city', 'district'],
      required: true,
    },
    parent: {
      type: 'relation',
      relation: 'manyToOne',
      target: 'api::geographic-zone.geographic-zone',
    },
    children: {
      type: 'relation',
      relation: 'oneToMany',
      target: 'api::geographic-zone.geographic-zone',
      mappedBy: 'parent',
    },
    geometry: {
      type: 'json', // GeoJSON polygon
    },
    population: {
      type: 'integer',
    },
    metadata: {
      type: 'json',
    },
  },
};
```

**User Location Extension**:
```typescript
// backend/src/extensions/users-permissions/content-types/user/schema.ts
export default {
  // Extend user model
  attributes: {
    location: {
      type: 'component',
      repeatable: false,
      component: 'user.location',
      required: true,
    },
    verifiedAddress: {
      type: 'boolean',
      default: false,
    },
    zones: {
      type: 'relation',
      relation: 'manyToMany',
      target: 'api::geographic-zone.geographic-zone',
    },
  },
};
```

### 2. Survey System Implementation

**Poll Content Type with Geographic Requirement**:
```typescript
// backend/src/api/poll/content-types/poll/schema.ts
export default {
  kind: 'collectionType',
  collectionName: 'polls',
  info: {
    singularName: 'poll',
    pluralName: 'polls',
    displayName: 'Poll',
  },
  options: {
    draftAndPublish: true,
  },
  attributes: {
    title: {
      type: 'string',
      required: true,
      maxLength: 200,
    },
    slug: {
      type: 'uid',
      targetField: 'title',
      required: true,
    },
    description: {
      type: 'richtext',
      required: true,
    },
    targetZones: {
      type: 'relation',
      relation: 'manyToMany',
      target: 'api::geographic-zone.geographic-zone',
      required: true, // MANDATORY
      min: 1,
    },
    restrictionType: {
      type: 'enumeration',
      enum: ['exact', 'includeChildren'],
      default: 'includeChildren',
    },
    questions: {
      type: 'relation',
      relation: 'oneToMany',
      target: 'api::question.question',
      mappedBy: 'poll',
    },
    startDate: {
      type: 'datetime',
      required: true,
    },
    endDate: {
      type: 'datetime',
      required: true,
    },
    creator: {
      type: 'relation',
      relation: 'manyToOne',
      target: 'plugin::users-permissions.user',
    },
    organization: {
      type: 'relation',
      relation: 'manyToOne',
      target: 'api::organization.organization',
    },
    template: {
      type: 'relation',
      relation: 'manyToOne',
      target: 'api::poll-template.poll-template',
    },
    settings: {
      type: 'component',
      repeatable: false,
      component: 'poll.settings',
    },
    analytics: {
      type: 'component',
      repeatable: false,
      component: 'poll.analytics',
    },
  },
};
```

### 3. Question System (5 Basic Types)

**Question Schema**:
```typescript
// backend/src/api/question/content-types/question/schema.ts
export default {
  attributes: {
    text: {
      type: 'string',
      required: true,
    },
    type: {
      type: 'enumeration',
      enum: ['single_choice', 'multiple_choice', 'text', 'rating', 'ranking'],
      required: true,
    },
    order: {
      type: 'integer',
      required: true,
    },
    required: {
      type: 'boolean',
      default: true,
    },
    options: {
      type: 'component',
      repeatable: true,
      component: 'question.option',
    },
    validation: {
      type: 'json', // Type-specific validation rules
    },
    poll: {
      type: 'relation',
      relation: 'manyToOne',
      target: 'api::poll.poll',
      inversedBy: 'questions',
    },
  },
};
```

### 4. Geographic Validation Service

**Location Verification**:
```typescript
// backend/src/api/poll/services/geographic-validator.ts
export default ({ strapi }) => ({
  async canUserAccessPoll(userId: string, pollId: string): Promise<boolean> {
    const user = await strapi.entityService.findOne(
      'plugin::users-permissions.user',
      userId,
      { populate: ['zones'] }
    );
    
    const poll = await strapi.entityService.findOne(
      'api::poll.poll',
      pollId,
      { populate: ['targetZones'] }
    );
    
    // Check if user's zones intersect with poll's target zones
    const userZoneIds = user.zones.map(z => z.id);
    const pollZoneIds = poll.targetZones.map(z => z.id);
    
    if (poll.restrictionType === 'exact') {
      return pollZoneIds.some(id => userZoneIds.includes(id));
    }
    
    // Include children zones
    const allPollZones = await this.getZonesWithChildren(pollZoneIds);
    return allPollZones.some(id => userZoneIds.includes(id));
  },
  
  async getZonesWithChildren(zoneIds: string[]): Promise<string[]> {
    const zones = await strapi.db.query('api::geographic-zone.geographic-zone')
      .findMany({
        where: {
          $or: [
            { id: { $in: zoneIds } },
            { parent: { id: { $in: zoneIds } } },
          ],
        },
      });
    
    return zones.map(z => z.id);
  },
});
```

### 5. SSO Integration

**Custom SSO Provider**:
```typescript
// backend/src/extensions/users-permissions/strapi-server.ts
export default (plugin) => {
  // Add SSO provider
  plugin.controllers.auth.ssoCallback = async (ctx) => {
    const { provider } = ctx.params;
    const { access_token } = ctx.query;
    
    if (provider === 'smatflow') {
      const ssoService = strapi.service('api::auth.sso-smatflow');
      const userData = await ssoService.getUserData(access_token);
      
      // Create or update user
      let user = await strapi.db.query('plugin::users-permissions.user')
        .findOne({ where: { email: userData.email } });
      
      if (!user) {
        user = await strapi.entityService.create(
          'plugin::users-permissions.user',
          {
            data: {
              ...userData,
              provider: 'smatflow',
              confirmed: true,
            },
          }
        );
      }
      
      // Generate JWT
      const jwt = strapi.plugins['users-permissions'].services.jwt.issue({
        id: user.id,
      });
      
      return { jwt, user };
    }
  };
  
  return plugin;
};
```

### 6. Response Collection System

**Response Service with Geographic Tracking**:
```typescript
// backend/src/api/response/services/response.ts
export default ({ strapi }) => ({
  async createResponse(data: any, user: any) {
    // Verify geographic access
    const geoValidator = strapi.service('api::poll.geographic-validator');
    const canAccess = await geoValidator.canUserAccessPoll(user.id, data.pollId);
    
    if (!canAccess) {
      throw new ApplicationError('Geographic restriction: You cannot access this poll');
    }
    
    // Create response session
    const session = await strapi.entityService.create(
      'api::response-session.response-session',
      {
        data: {
          user: user.id,
          poll: data.pollId,
          startedAt: new Date(),
          userZone: user.zones[0]?.id, // Primary zone
          metadata: {
            ip: data.ip,
            userAgent: data.userAgent,
          },
        },
      }
    );
    
    // Save individual responses
    const responses = await Promise.all(
      data.responses.map(response =>
        strapi.entityService.create('api::response.response', {
          data: {
            ...response,
            session: session.id,
            user: user.id,
          },
        })
      )
    );
    
    // Update session completion
    await strapi.entityService.update(
      'api::response-session.response-session',
      session.id,
      {
        data: {
          completedAt: new Date(),
          responses: responses.map(r => r.id),
        },
      }
    );
    
    // Trigger analytics update
    await strapi.service('api::poll.analytics').updatePollStats(data.pollId);
    
    return session;
  },
});
```

### 7. Real-time Analytics

**Analytics Service**:
```typescript
// backend/src/api/poll/services/analytics.ts
export default ({ strapi }) => ({
  async updatePollStats(pollId: string) {
    const stats = await this.calculateStats(pollId);
    
    await strapi.entityService.update('api::poll.poll', pollId, {
      data: {
        analytics: {
          totalResponses: stats.totalResponses,
          completionRate: stats.completionRate,
          averageTime: stats.averageTime,
          geographicDistribution: stats.geoDistribution,
          lastUpdated: new Date(),
        },
      },
    });
    
    // Emit real-time update
    strapi.io.emit(`poll:${pollId}:stats`, stats);
  },
  
  async calculateStats(pollId: string) {
    const sessions = await strapi.db.query('api::response-session.response-session')
      .findMany({
        where: { poll: pollId, completedAt: { $ne: null } },
        populate: ['userZone'],
      });
    
    const geoDistribution = sessions.reduce((acc, session) => {
      const zone = session.userZone?.name || 'Unknown';
      acc[zone] = (acc[zone] || 0) + 1;
      return acc;
    }, {});
    
    return {
      totalResponses: sessions.length,
      completionRate: this.calculateCompletionRate(sessions),
      averageTime: this.calculateAverageTime(sessions),
      geoDistribution,
    };
  },
});
```

### 8. Frontend Architecture

**Project Structure**:
```
frontend/
├── src/
│   ├── components/
│   │   ├── common/
│   │   ├── survey/
│   │   │   ├── QuestionRenderer.tsx
│   │   │   ├── SurveyProgress.tsx
│   │   │   └── GeographicRestriction.tsx
│   │   ├── geographic/
│   │   │   ├── ZoneSelector.tsx
│   │   │   ├── AddressValidator.tsx
│   │   │   └── LocationMap.tsx
│   │   └── auth/
│   │       └── SSOLogin.tsx
│   ├── hooks/
│   │   ├── useGeographic.ts
│   │   ├── usePoll.ts
│   │   └── useAuth.ts
│   ├── services/
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   └── geographic.ts
│   ├── types/
│   │   └── index.ts
│   └── pages/
│       ├── public/
│       ├── participant/
│       └── organization/
```

**Geographic Zone Selector Component**:
```typescript
// frontend/src/components/geographic/ZoneSelector.tsx
import React, { useState } from 'react';
import { MapContainer, TileLayer, Polygon } from 'react-leaflet';
import { useGeographicZones } from '@/hooks/useGeographic';

interface ZoneSelectorProps {
  onSelect: (zones: string[]) => void;
  multiple?: boolean;
  required?: boolean;
}

export const ZoneSelector: React.FC<ZoneSelectorProps> = ({
  onSelect,
  multiple = true,
  required = true,
}) => {
  const { zones, loading } = useGeographicZones();
  const [selected, setSelected] = useState<string[]>([]);
  
  const handleZoneClick = (zoneId: string) => {
    if (multiple) {
      const newSelected = selected.includes(zoneId)
        ? selected.filter(id => id !== zoneId)
        : [...selected, zoneId];
      setSelected(newSelected);
      onSelect(newSelected);
    } else {
      setSelected([zoneId]);
      onSelect([zoneId]);
    }
  };
  
  return (
    <div className="zone-selector">
      <MapContainer center={[46.603354, 1.888334]} zoom={6}>
        <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
        {zones.map(zone => (
          <Polygon
            key={zone.id}
            positions={zone.geometry.coordinates}
            pathOptions={{
              color: selected.includes(zone.id) ? '#3b82f6' : '#6b7280',
              fillOpacity: selected.includes(zone.id) ? 0.5 : 0.2,
            }}
            eventHandlers={{
              click: () => handleZoneClick(zone.id),
            }}
          />
        ))}
      </MapContainer>
      
      <div className="zone-list">
        {zones.map(zone => (
          <label key={zone.id} className="zone-item">
            <input
              type={multiple ? 'checkbox' : 'radio'}
              name="zones"
              checked={selected.includes(zone.id)}
              onChange={() => handleZoneClick(zone.id)}
              required={required && selected.length === 0}
            />
            <span>{zone.name} ({zone.type})</span>
          </label>
        ))}
      </div>
    </div>
  );
};
```

---

## 📋 Phase 2: Enhanced Features & API (Weeks 13-18)

### 1. Extended Question Types (20 Types)

**Question Type Registry**:
```typescript
// backend/src/api/question/services/question-types.ts
export const QuestionTypes = {
  // Choice Questions
  SINGLE_CHOICE: {
    key: 'single_choice',
    validator: (value: any) => typeof value === 'string',
    component: 'RadioGroup',
  },
  MULTIPLE_CHOICE: {
    key: 'multiple_choice',
    validator: (value: any) => Array.isArray(value),
    component: 'CheckboxGroup',
  },
  DROPDOWN: {
    key: 'dropdown',
    validator: (value: any) => typeof value === 'string',
    component: 'Select',
  },
  
  // Text Questions
  SHORT_TEXT: {
    key: 'short_text',
    validator: (value: any) => typeof value === 'string' && value.length <= 255,
    component: 'TextInput',
  },
  LONG_TEXT: {
    key: 'long_text',
    validator: (value: any) => typeof value === 'string',
    component: 'TextArea',
  },
  EMAIL: {
    key: 'email',
    validator: (value: any) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    component: 'EmailInput',
  },
  PHONE: {
    key: 'phone',
    validator: (value: any) => /^[\d\s\-\+\(\)]+$/.test(value),
    component: 'PhoneInput',
  },
  NUMBER: {
    key: 'number',
    validator: (value: any) => !isNaN(Number(value)),
    component: 'NumberInput',
  },
  
  // Rating Questions
  LIKERT: {
    key: 'likert',
    validator: (value: any) => value >= 1 && value <= 5,
    component: 'LikertScale',
  },
  STAR_RATING: {
    key: 'star_rating',
    validator: (value: any) => value >= 1 && value <= 5,
    component: 'StarRating',
  },
  NPS: {
    key: 'nps',
    validator: (value: any) => value >= 0 && value <= 10,
    component: 'NPSScale',
  },
  SLIDER: {
    key: 'slider',
    validator: (value: any, config: any) => 
      value >= config.min && value <= config.max,
    component: 'Slider',
  },
  
  // Ranking Questions
  RANKING: {
    key: 'ranking',
    validator: (value: any) => Array.isArray(value),
    component: 'RankingList',
  },
  MATRIX: {
    key: 'matrix',
    validator: (value: any) => typeof value === 'object',
    component: 'MatrixGrid',
  },
  
  // Media Questions
  IMAGE_UPLOAD: {
    key: 'image_upload',
    validator: (value: any) => value?.mime?.startsWith('image/'),
    component: 'ImageUpload',
  },
  FILE_UPLOAD: {
    key: 'file_upload',
    validator: (value: any) => value?.size <= 10485760, // 10MB
    component: 'FileUpload',
  },
  VIDEO_RESPONSE: {
    key: 'video_response',
    validator: (value: any) => value?.duration <= 120, // 2 minutes
    component: 'VideoRecorder',
  },
  
  // Advanced Questions
  SIGNATURE: {
    key: 'signature',
    validator: (value: any) => value?.startsWith('data:image/png;base64,'),
    component: 'SignaturePad',
  },
  DRAWING: {
    key: 'drawing',
    validator: (value: any) => value?.startsWith('data:image/png;base64,'),
    component: 'DrawingCanvas',
  },
  GEOLOCATION: {
    key: 'geolocation',
    validator: (value: any) => value?.lat && value?.lng,
    component: 'LocationPicker',
  },
};
```

### 2. Template System

**Poll Template Schema**:
```typescript
// backend/src/api/poll-template/content-types/poll-template/schema.ts
export default {
  attributes: {
    name: {
      type: 'string',
      required: true,
    },
    description: {
      type: 'text',
    },
    category: {
      type: 'enumeration',
      enum: ['hr', 'marketing', 'political', 'event', 'research', 'custom'],
      required: true,
    },
    methodology: {
      type: 'enumeration',
      enum: ['nps', 'csat', 'enps', 'ces', 'generic'],
    },
    questions: {
      type: 'json', // Structured question definitions
      required: true,
    },
    settings: {
      type: 'json', // Default poll settings
    },
    isPublic: {
      type: 'boolean',
      default: true,
    },
    usageCount: {
      type: 'integer',
      default: 0,
    },
    tags: {
      type: 'json',
    },
    creator: {
      type: 'relation',
      relation: 'manyToOne',
      target: 'plugin::users-permissions.user',
    },
  },
};
```

**Template Service**:
```typescript
// backend/src/api/poll-template/services/template-engine.ts
export default ({ strapi }) => ({
  async applyTemplate(templateId: string, pollData: any) {
    const template = await strapi.entityService.findOne(
      'api::poll-template.poll-template',
      templateId
    );
    
    // Create poll with template
    const poll = await strapi.entityService.create('api::poll.poll', {
      data: {
        ...pollData,
        template: templateId,
        settings: { ...template.settings, ...pollData.settings },
      },
    });
    
    // Create questions from template
    const questions = await Promise.all(
      template.questions.map((q, index) =>
        strapi.entityService.create('api::question.question', {
          data: {
            ...q,
            poll: poll.id,
            order: index,
          },
        })
      )
    );
    
    // Increment usage count
    await strapi.entityService.update(
      'api::poll-template.poll-template',
      templateId,
      {
        data: {
          usageCount: template.usageCount + 1,
        },
      }
    );
    
    return { poll, questions };
  },
  
  async createMethodologyTemplate(methodology: string) {
    const methodologies = {
      nps: {
        name: 'Net Promoter Score',
        questions: [
          {
            text: 'How likely are you to recommend our product/service to a friend or colleague?',
            type: 'nps',
            required: true,
          },
          {
            text: 'What is the primary reason for your score?',
            type: 'long_text',
            required: false,
          },
        ],
      },
      csat: {
        name: 'Customer Satisfaction',
        questions: [
          {
            text: 'How satisfied are you with our product/service?',
            type: 'likert',
            required: true,
            options: [
              { value: 1, label: 'Very Dissatisfied' },
              { value: 2, label: 'Dissatisfied' },
              { value: 3, label: 'Neutral' },
              { value: 4, label: 'Satisfied' },
              { value: 5, label: 'Very Satisfied' },
            ],
          },
        ],
      },
      // Add more methodologies
    };
    
    return methodologies[methodology];
  },
});
```

### 3. REST API v1 Implementation

**API Routes Configuration**:
```typescript
// backend/src/api/v1/routes/index.ts
export default {
  routes: [
    // Poll endpoints
    {
      method: 'GET',
      path: '/v1/polls',
      handler: 'api::v1.polls.find',
      config: {
        policies: ['api::v1.api-key-auth'],
        middlewares: ['api::v1.rate-limit'],
      },
    },
    {
      method: 'GET',
      path: '/v1/polls/:id',
      handler: 'api::v1.polls.findOne',
      config: {
        policies: ['api::v1.api-key-auth'],
        middlewares: ['api::v1.rate-limit'],
      },
    },
    {
      method: 'POST',
      path: '/v1/polls',
      handler: 'api::v1.polls.create',
      config: {
        policies: ['api::v1.api-key-auth'],
        middlewares: ['api::v1.rate-limit'],
      },
    },
    {
      method: 'PUT',
      path: '/v1/polls/:id',
      handler: 'api::v1.polls.update',
      config: {
        policies: ['api::v1.api-key-auth'],
        middlewares: ['api::v1.rate-limit'],
      },
    },
    {
      method: 'DELETE',
      path: '/v1/polls/:id',
      handler: 'api::v1.polls.delete',
      config: {
        policies: ['api::v1.api-key-auth'],
        middlewares: ['api::v1.rate-limit'],
      },
    },
    
    // Response endpoints
    {
      method: 'POST',
      path: '/v1/polls/:id/responses',
      handler: 'api::v1.responses.create',
      config: {
        policies: ['api::v1.api-key-auth'],
        middlewares: ['api::v1.rate-limit'],
      },
    },
    {
      method: 'GET',
      path: '/v1/polls/:id/analytics',
      handler: 'api::v1.analytics.getPollStats',
      config: {
        policies: ['api::v1.api-key-auth'],
        middlewares: ['api::v1.rate-limit'],
      },
    },
    
    // Webhook management
    {
      method: 'POST',
      path: '/v1/webhooks',
      handler: 'api::v1.webhooks.create',
      config: {
        policies: ['api::v1.api-key-auth'],
      },
    },
  ],
};
```

**API Key Authentication Policy**:
```typescript
// backend/src/api/v1/policies/api-key-auth.ts
export default async (ctx, config, { strapi }) => {
  const apiKey = ctx.request.header['x-api-key'];
  
  if (!apiKey) {
    return ctx.unauthorized('API key is required');
  }
  
  const key = await strapi.db.query('api::api-key.api-key').findOne({
    where: { key: apiKey, active: true },
    populate: ['organization'],
  });
  
  if (!key) {
    return ctx.unauthorized('Invalid API key');
  }
  
  // Check rate limits
  const rateLimiter = strapi.service('api::v1.rate-limiter');
  const allowed = await rateLimiter.checkApiKeyLimit(key.id);
  
  if (!allowed) {
    return ctx.tooManyRequests('Rate limit exceeded');
  }
  
  // Set organization context
  ctx.state.apiKey = key;
  ctx.state.organization = key.organization;
  
  return true;
};
```

### 4. Webhook System

**Webhook Service**:
```typescript
// backend/src/api/webhook/services/webhook.ts
import crypto from 'crypto';
import axios from 'axios';

export default ({ strapi }) => ({
  async trigger(event: string, data: any) {
    const webhooks = await strapi.db.query('api::webhook.webhook').findMany({
      where: {
        events: { $contains: event },
        active: true,
      },
    });
    
    await Promise.all(
      webhooks.map(webhook => this.sendWebhook(webhook, event, data))
    );
  },
  
  async sendWebhook(webhook: any, event: string, data: any) {
    const payload = {
      event,
      data,
      timestamp: new Date().toISOString(),
      webhookId: webhook.id,
    };
    
    const signature = this.generateSignature(payload, webhook.secret);
    
    try {
      await axios.post(webhook.url, payload, {
        headers: {
          'X-Webhook-Signature': signature,
          'X-Webhook-Event': event,
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      });
      
      await this.logDelivery(webhook.id, 'success', payload);
    } catch (error) {
      await this.logDelivery(webhook.id, 'failed', payload, error.message);
      await this.scheduleRetry(webhook, payload);
    }
  },
  
  generateSignature(payload: any, secret: string): string {
    return crypto
      .createHmac('sha256', secret)
      .update(JSON.stringify(payload))
      .digest('hex');
  },
  
  async scheduleRetry(webhook: any, payload: any) {
    const queue = strapi.service('api::queue.queue');
    
    await queue.add('webhook-retry', {
      webhookId: webhook.id,
      payload,
      attempt: 1,
    }, {
      delay: 60000, // 1 minute
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 60000,
      },
    });
  },
});
```

### 5. Multi-Channel Notifications

**Notification Service**:
```typescript
// backend/src/api/notification/services/notification.ts
export default ({ strapi }) => ({
  async send(channel: string, recipient: string, template: string, data: any) {
    const handlers = {
      email: this.sendEmail,
      sms: this.sendSMS,
      push: this.sendPush,
      inapp: this.sendInApp,
    };
    
    const handler = handlers[channel];
    if (!handler) {
      throw new Error(`Unknown notification channel: ${channel}`);
    }
    
    return handler.call(this, recipient, template, data);
  },
  
  async sendEmail(recipient: string, template: string, data: any) {
    const emailTemplate = await strapi.db.query('api::email-template.email-template')
      .findOne({ where: { key: template } });
    
    const html = await strapi.service('api::template.template')
      .render(emailTemplate.html, data);
    
    return strapi.plugins['email'].services.email.send({
      to: recipient,
      subject: emailTemplate.subject,
      html,
    });
  },
  
  async sendSMS(recipient: string, template: string, data: any) {
    const smsTemplate = await strapi.db.query('api::sms-template.sms-template')
      .findOne({ where: { key: template } });
    
    const message = await strapi.service('api::template.template')
      .render(smsTemplate.text, data);
    
    const twilio = strapi.service('api::twilio.twilio');
    return twilio.sendSMS(recipient, message);
  },
  
  async sendBatch(notifications: any[]) {
    const queue = strapi.service('api::queue.queue');
    
    await queue.addBulk(
      notifications.map(n => ({
        name: 'send-notification',
        data: n,
      }))
    );
  },
});
```

---

## 📋 Phase 3: Gamification System (Weeks 19-26)

### 1. User Profile Extension for Gamification

**Extended User Schema**:
```typescript
// backend/src/extensions/users-permissions/content-types/user/schema.ts
export default {
  attributes: {
    // Existing fields...
    
    // Gamification fields
    experience: {
      type: 'integer',
      default: 0,
      min: 0,
    },
    level: {
      type: 'integer',
      default: 1,
      min: 1,
      max: 100,
    },
    points: {
      type: 'integer',
      default: 0,
      min: 0,
    },
    streak: {
      type: 'integer',
      default: 0,
      min: 0,
    },
    lastActivityDate: {
      type: 'date',
    },
    badges: {
      type: 'relation',
      relation: 'manyToMany',
      target: 'api::badge.badge',
      inversedBy: 'users',
    },
    achievements: {
      type: 'relation',
      relation: 'oneToMany',
      target: 'api::achievement.achievement',
      mappedBy: 'user',
    },
    leaderboardOptIn: {
      type: 'boolean',
      default: true,
    },
  },
};
```

### 2. Gamification Engine

**Points System**:
```typescript
// backend/src/api/gamification/services/points-engine.ts
export default ({ strapi }) => ({
  pointRules: {
    POLL_COMPLETED: 50,
    FIRST_POLL: 100,
    DAILY_STREAK: 10,
    WEEKLY_STREAK: 50,
    PERFECT_WEEK: 200,
    QUICK_RESPONSE: 20, // Under 2 minutes
    DETAILED_RESPONSE: 30, // Long text answers
    PHOTO_UPLOAD: 25,
    VIDEO_RESPONSE: 40,
    INVITE_FRIEND: 100,
    FRIEND_JOINED: 200,
  },
  
  async awardPoints(userId: string, action: string, metadata?: any) {
    const points = this.pointRules[action] || 0;
    if (points === 0) return;
    
    const user = await strapi.entityService.findOne(
      'plugin::users-permissions.user',
      userId,
      { populate: ['badges'] }
    );
    
    // Calculate multipliers
    let multiplier = 1;
    if (metadata?.streak > 7) multiplier *= 1.5;
    if (metadata?.weekend) multiplier *= 2;
    if (user.badges.some(b => b.key === 'super_contributor')) multiplier *= 1.2;
    
    const finalPoints = Math.floor(points * multiplier);
    
    // Update user points and XP
    const newTotal = user.points + finalPoints;
    const newXP = user.experience + finalPoints;
    const newLevel = this.calculateLevel(newXP);
    
    await strapi.entityService.update(
      'plugin::users-permissions.user',
      userId,
      {
        data: {
          points: newTotal,
          experience: newXP,
          level: newLevel,
          lastActivityDate: new Date(),
        },
      }
    );
    
    // Check for level up
    if (newLevel > user.level) {
      await this.handleLevelUp(userId, newLevel);
    }
    
    // Check for new badges
    await strapi.service('api::gamification.badge-engine')
      .checkBadges(userId);
    
    // Log points transaction
    await strapi.entityService.create('api::points-log.points-log', {
      data: {
        user: userId,
        action,
        points: finalPoints,
        metadata,
      },
    });
    
    // Emit real-time update
    strapi.io.to(`user:${userId}`).emit('points:awarded', {
      action,
      points: finalPoints,
      totalPoints: newTotal,
      level: newLevel,
    });
    
    return { points: finalPoints, totalPoints: newTotal, level: newLevel };
  },
  
  calculateLevel(experience: number): number {
    // Level progression: 0-100-************-1500-2100-2800-3600-4500-5500...
    const levels = [0, 100, 300, 600, 1000, 1500, 2100, 2800, 3600, 4500, 5500,
                   6600, 7800, 9100, 10500, 12000, 13600, 15300, 17100, 19000];
    
    for (let i = levels.length - 1; i >= 0; i--) {
      if (experience >= levels[i]) {
        return i + 1;
      }
    }
    return 1;
  },
  
  async handleLevelUp(userId: string, newLevel: number) {
    // Create notification
    await strapi.service('api::notification.notification').send(
      'inapp',
      userId,
      'level_up',
      { level: newLevel }
    );
    
    // Award level-up bonus
    const bonus = newLevel * 100;
    await strapi.entityService.update(
      'plugin::users-permissions.user',
      userId,
      {
        data: {
          points: { $inc: bonus },
        },
      }
    );
    
    // Emit celebration
    strapi.io.to(`user:${userId}`).emit('celebration', {
      type: 'level_up',
      level: newLevel,
      bonus,
    });
  },
});
```

### 3. Badge System

**Badge Schema**:
```typescript
// backend/src/api/badge/content-types/badge/schema.ts
export default {
  attributes: {
    key: {
      type: 'string',
      required: true,
      unique: true,
    },
    name: {
      type: 'string',
      required: true,
    },
    description: {
      type: 'text',
      required: true,
    },
    icon: {
      type: 'media',
      required: true,
    },
    category: {
      type: 'enumeration',
      enum: ['participation', 'quality', 'community', 'special', 'geographic'],
      required: true,
    },
    rarity: {
      type: 'enumeration',
      enum: ['common', 'uncommon', 'rare', 'epic', 'legendary'],
      required: true,
    },
    criteria: {
      type: 'json', // Automated criteria checking
      required: true,
    },
    points: {
      type: 'integer',
      default: 0,
    },
    users: {
      type: 'relation',
      relation: 'manyToMany',
      target: 'plugin::users-permissions.user',
      mappedBy: 'badges',
    },
  },
};
```

**Badge Engine**:
```typescript
// backend/src/api/gamification/services/badge-engine.ts
export default ({ strapi }) => ({
  async checkBadges(userId: string) {
    const user = await strapi.entityService.findOne(
      'plugin::users-permissions.user',
      userId,
      {
        populate: ['badges', 'responses', 'location'],
      }
    );
    
    const allBadges = await strapi.db.query('api::badge.badge').findMany();
    const currentBadgeIds = user.badges.map(b => b.id);
    
    for (const badge of allBadges) {
      if (!currentBadgeIds.includes(badge.id)) {
        const earned = await this.checkCriteria(user, badge.criteria);
        if (earned) {
          await this.awardBadge(userId, badge.id);
        }
      }
    }
  },
  
  async checkCriteria(user: any, criteria: any): Promise<boolean> {
    const checks = {
      pollsCompleted: async (required: number) => {
        const count = await strapi.db.query('api::response-session.response-session')
          .count({ where: { user: user.id, completedAt: { $ne: null } } });
        return count >= required;
      },
      
      level: (required: number) => user.level >= required,
      
      streak: (required: number) => user.streak >= required,
      
      location: (required: string) => {
        return user.location?.city === required || 
               user.location?.region === required;
      },
      
      perfectWeek: async () => {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        
        const days = await strapi.db.query('api::response-session.response-session')
          .findMany({
            where: {
              user: user.id,
              createdAt: { $gte: weekAgo },
            },
            select: ['createdAt'],
          });
        
        const uniqueDays = new Set(days.map(d => 
          d.createdAt.toISOString().split('T')[0]
        ));
        
        return uniqueDays.size === 7;
      },
      
      // Add more criteria checks
    };
    
    for (const [key, value] of Object.entries(criteria)) {
      const check = checks[key];
      if (check && !(await check(value))) {
        return false;
      }
    }
    
    return true;
  },
  
  async awardBadge(userId: string, badgeId: string) {
    const badge = await strapi.entityService.findOne(
      'api::badge.badge',
      badgeId
    );
    
    // Create achievement record
    await strapi.entityService.create('api::achievement.achievement', {
      data: {
        user: userId,
        badge: badgeId,
        earnedAt: new Date(),
      },
    });
    
    // Update user badges
    await strapi.db.query('plugin::users-permissions.user').update({
      where: { id: userId },
      data: {
        badges: {
          connect: [{ id: badgeId }],
        },
      },
    });
    
    // Award badge points
    if (badge.points > 0) {
      await strapi.service('api::gamification.points-engine')
        .awardPoints(userId, 'BADGE_EARNED', { badge: badge.key });
    }
    
    // Send notification
    await strapi.service('api::notification.notification').send(
      'inapp',
      userId,
      'badge_earned',
      { badge }
    );
    
    // Emit real-time update
    strapi.io.to(`user:${userId}`).emit('badge:earned', {
      badge,
      timestamp: new Date(),
    });
  },
});
```

### 4. Challenge System

**Challenge Schema**:
```typescript
// backend/src/api/challenge/content-types/challenge/schema.ts
export default {
  attributes: {
    name: {
      type: 'string',
      required: true,
    },
    description: {
      type: 'text',
      required: true,
    },
    type: {
      type: 'enumeration',
      enum: ['daily', 'weekly', 'monthly', 'special'],
      required: true,
    },
    startDate: {
      type: 'datetime',
      required: true,
    },
    endDate: {
      type: 'datetime',
      required: true,
    },
    criteria: {
      type: 'json',
      required: true,
    },
    reward: {
      type: 'component',
      repeatable: false,
      component: 'challenge.reward',
    },
    participants: {
      type: 'relation',
      relation: 'manyToMany',
      target: 'plugin::users-permissions.user',
    },
    winners: {
      type: 'relation',
      relation: 'manyToMany',
      target: 'plugin::users-permissions.user',
    },
    targetZones: {
      type: 'relation',
      relation: 'manyToMany',
      target: 'api::geographic-zone.geographic-zone',
    },
  },
};
```

**Challenge Service**:
```typescript
// backend/src/api/gamification/services/challenge-engine.ts
export default ({ strapi }) => ({
  async createDailyChallenges() {
    const challenges = [
      {
        name: 'Speed Demon',
        description: 'Complete 3 polls in under 5 minutes total',
        criteria: { polls: 3, timeLimit: 300 },
        reward: { points: 100, badge: 'speed_demon' },
      },
      {
        name: 'Quality Contributor',
        description: 'Provide detailed responses (50+ words) in 2 polls',
        criteria: { detailedResponses: 2, minWords: 50 },
        reward: { points: 150 },
      },
      {
        name: 'Explorer',
        description: 'Participate in polls from 3 different categories',
        criteria: { categories: 3 },
        reward: { points: 200 },
      },
    ];
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Select random challenge
    const challenge = challenges[Math.floor(Math.random() * challenges.length)];
    
    return strapi.entityService.create('api::challenge.challenge', {
      data: {
        ...challenge,
        type: 'daily',
        startDate: today,
        endDate: tomorrow,
      },
    });
  },
  
  async checkChallengeProgress(userId: string, challengeId: string) {
    const challenge = await strapi.entityService.findOne(
      'api::challenge.challenge',
      challengeId,
      { populate: ['participants', 'winners'] }
    );
    
    if (!challenge || challenge.winners.some(w => w.id === userId)) {
      return null;
    }
    
    const progress = await this.calculateProgress(userId, challenge);
    
    if (progress.completed) {
      await this.completeChallenge(userId, challengeId);
    }
    
    return progress;
  },
  
  async completeChallenge(userId: string, challengeId: string) {
    const challenge = await strapi.entityService.findOne(
      'api::challenge.challenge',
      challengeId
    );
    
    // Add to winners
    await strapi.db.query('api::challenge.challenge').update({
      where: { id: challengeId },
      data: {
        winners: {
          connect: [{ id: userId }],
        },
      },
    });
    
    // Award rewards
    if (challenge.reward.points) {
      await strapi.service('api::gamification.points-engine')
        .awardPoints(userId, 'CHALLENGE_COMPLETED', {
          challenge: challenge.name,
          points: challenge.reward.points,
        });
    }
    
    if (challenge.reward.badge) {
      const badge = await strapi.db.query('api::badge.badge').findOne({
        where: { key: challenge.reward.badge },
      });
      if (badge) {
        await strapi.service('api::gamification.badge-engine')
          .awardBadge(userId, badge.id);
      }
    }
    
    // Notify user
    strapi.io.to(`user:${userId}`).emit('challenge:completed', {
      challenge,
      reward: challenge.reward,
    });
  },
});
```

### 5. Leaderboard System

**Leaderboard Service**:
```typescript
// backend/src/api/gamification/services/leaderboard.ts
export default ({ strapi }) => ({
  async getLeaderboard(type: string, scope: string, limit: number = 100) {
    const cacheKey = `leaderboard:${type}:${scope}`;
    const redis = strapi.service('api::cache.redis');
    
    // Try cache first
    const cached = await redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }
    
    let query;
    const now = new Date();
    
    switch (type) {
      case 'weekly':
        const weekStart = new Date(now);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        weekStart.setHours(0, 0, 0, 0);
        
        query = {
          where: {
            createdAt: { $gte: weekStart },
            leaderboardOptIn: true,
          },
        };
        break;
        
      case 'monthly':
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        
        query = {
          where: {
            createdAt: { $gte: monthStart },
            leaderboardOptIn: true,
          },
        };
        break;
        
      case 'alltime':
      default:
        query = {
          where: { leaderboardOptIn: true },
        };
    }
    
    // Add geographic scope
    if (scope !== 'global') {
      const zone = await strapi.db.query('api::geographic-zone.geographic-zone')
        .findOne({ where: { code: scope } });
      
      if (zone) {
        query.where.zones = { id: zone.id };
      }
    }
    
    // Get users with points
    const users = await strapi.db.query('plugin::users-permissions.user')
      .findMany({
        ...query,
        select: ['id', 'username', 'points', 'level', 'experience'],
        populate: ['location', 'badges'],
        orderBy: { points: 'desc' },
        limit,
      });
    
    const leaderboard = users.map((user, index) => ({
      rank: index + 1,
      userId: user.id,
      username: user.username,
      points: user.points,
      level: user.level,
      experience: user.experience,
      location: user.location?.city || 'Unknown',
      badges: user.badges.length,
      trend: 'stable', // Calculate from historical data
    }));
    
    // Cache for 1 hour
    await redis.set(cacheKey, JSON.stringify(leaderboard), 3600);
    
    return leaderboard;
  },
  
  async getUserRank(userId: string, type: string, scope: string) {
    const leaderboard = await this.getLeaderboard(type, scope, 1000);
    const userEntry = leaderboard.find(entry => entry.userId === userId);
    
    if (!userEntry) {
      // User not in top 1000
      const userCount = await strapi.db.query('plugin::users-permissions.user')
        .count({ where: { leaderboardOptIn: true } });
      
      return {
        rank: userCount,
        percentile: 0,
      };
    }
    
    return {
      rank: userEntry.rank,
      percentile: Math.round((1 - userEntry.rank / leaderboard.length) * 100),
      ...userEntry,
    };
  },
  
  async updateLeaderboards() {
    const types = ['weekly', 'monthly', 'alltime'];
    const scopes = ['global', 'FR', 'FR-75', 'FR-13']; // Add more as needed
    
    for (const type of types) {
      for (const scope of scopes) {
        await this.getLeaderboard(type, scope, 100);
      }
    }
  },
});
```

### 6. Real-time Socket.IO Integration

**Socket.IO Setup**:
```typescript
// backend/src/index.ts
import { Server } from 'socket.io';

export default {
  register({ strapi }) {
    // Socket.IO will be initialized in bootstrap
  },
  
  async bootstrap({ strapi }) {
    const io = new Server(strapi.server.httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL,
        credentials: true,
      },
    });
    
    // Middleware for authentication
    io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        const { id } = await strapi.plugins['users-permissions']
          .services.jwt.verify(token);
        
        socket.userId = id;
        socket.join(`user:${id}`);
        
        // Join geographic rooms
        const user = await strapi.entityService.findOne(
          'plugin::users-permissions.user',
          id,
          { populate: ['zones'] }
        );
        
        user.zones.forEach(zone => {
          socket.join(`zone:${zone.id}`);
        });
        
        next();
      } catch (err) {
        next(new Error('Authentication failed'));
      }
    });
    
    // Event handlers
    io.on('connection', (socket) => {
      console.log(`User ${socket.userId} connected`);
      
      socket.on('poll:join', async (pollId) => {
        // Verify access
        const canAccess = await strapi.service('api::poll.geographic-validator')
          .canUserAccessPoll(socket.userId, pollId);
        
        if (canAccess) {
          socket.join(`poll:${pollId}`);
          socket.emit('poll:joined', { pollId });
        }
      });
      
      socket.on('poll:leave', (pollId) => {
        socket.leave(`poll:${pollId}`);
      });
      
      socket.on('disconnect', () => {
        console.log(`User ${socket.userId} disconnected`);
      });
    });
    
    strapi.io = io;
  },
};
```

### 7. Frontend Gamification Components

**User Profile Component**:
```typescript
// frontend/src/components/gamification/UserProfile.tsx
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useUserStats } from '@/hooks/useGamification';

export const UserProfile: React.FC = () => {
  const { user, stats, loading } = useUserStats();
  
  if (loading) return <ProfileSkeleton />;
  
  const nextLevelXP = calculateNextLevelXP(user.level);
  const progressPercent = (user.experience % nextLevelXP) / nextLevelXP * 100;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="user-profile"
    >
      <div className="profile-header">
        <Avatar user={user} />
        <div className="user-info">
          <h2>{user.username}</h2>
          <div className="level-badge">
            <span>Level {user.level}</span>
            <span className="title">{getLevelTitle(user.level)}</span>
          </div>
        </div>
      </div>
      
      <div className="experience-bar">
        <motion.div
          className="progress"
          initial={{ width: 0 }}
          animate={{ width: `${progressPercent}%` }}
          transition={{ duration: 1, ease: 'easeOut' }}
        />
        <span className="xp-text">
          {user.experience % nextLevelXP} / {nextLevelXP} XP
        </span>
      </div>
      
      <div className="stats-grid">
        <StatCard
          icon="🏆"
          label="Total Points"
          value={user.points.toLocaleString()}
        />
        <StatCard
          icon="🔥"
          label="Current Streak"
          value={`${user.streak} days`}
        />
        <StatCard
          icon="📊"
          label="Polls Completed"
          value={stats.pollsCompleted}
        />
        <StatCard
          icon="🥇"
          label="Rank"
          value={`#${stats.rank}`}
        />
      </div>
      
      <BadgeShowcase badges={user.badges} />
      
      <RecentAchievements achievements={user.achievements} />
    </motion.div>
  );
};
```

**Real-time Notifications**:
```typescript
// frontend/src/components/gamification/RealtimeNotifications.tsx
import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSocket } from '@/hooks/useSocket';
import confetti from 'canvas-confetti';

interface Notification {
  id: string;
  type: string;
  data: any;
}

export const RealtimeNotifications: React.FC = () => {
  const socket = useSocket();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  
  useEffect(() => {
    if (!socket) return;
    
    // Points awarded
    socket.on('points:awarded', (data) => {
      addNotification({
        type: 'points',
        data,
      });
    });
    
    // Badge earned
    socket.on('badge:earned', (data) => {
      addNotification({
        type: 'badge',
        data,
      });
      
      // Trigger confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
      });
    });
    
    // Level up
    socket.on('celebration', (data) => {
      if (data.type === 'level_up') {
        addNotification({
          type: 'levelUp',
          data,
        });
        
        // Epic confetti
        const duration = 3000;
        const animationEnd = Date.now() + duration;
        
        const randomInRange = (min: number, max: number) => {
          return Math.random() * (max - min) + min;
        };
        
        const interval = setInterval(() => {
          const timeLeft = animationEnd - Date.now();
          
          if (timeLeft <= 0) {
            return clearInterval(interval);
          }
          
          confetti({
            particleCount: 50,
            startVelocity: 30,
            spread: 360,
            ticks: 60,
            origin: {
              x: randomInRange(0.1, 0.9),
              y: Math.random() - 0.2,
            },
          });
        }, 250);
      }
    });
    
    // Challenge completed
    socket.on('challenge:completed', (data) => {
      addNotification({
        type: 'challenge',
        data,
      });
    });
    
    return () => {
      socket.off('points:awarded');
      socket.off('badge:earned');
      socket.off('celebration');
      socket.off('challenge:completed');
    };
  }, [socket]);
  
  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { ...notification, id }]);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, 5000);
  };
  
  return (
    <div className="notification-container">
      <AnimatePresence>
        {notifications.map(notification => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className={`notification notification-${notification.type}`}
          >
            {renderNotification(notification)}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

const renderNotification = (notification: Notification) => {
  switch (notification.type) {
    case 'points':
      return (
        <>
          <span className="icon">💰</span>
          <div>
            <strong>+{notification.data.points} Points!</strong>
            <p>{notification.data.action}</p>
          </div>
        </>
      );
      
    case 'badge':
      return (
        <>
          <img src={notification.data.badge.icon} alt="" className="badge-icon" />
          <div>
            <strong>New Badge Earned!</strong>
            <p>{notification.data.badge.name}</p>
          </div>
        </>
      );
      
    case 'levelUp':
      return (
        <>
          <span className="icon">🎉</span>
          <div>
            <strong>Level {notification.data.level}!</strong>
            <p>+{notification.data.bonus} bonus points</p>
          </div>
        </>
      );
      
    case 'challenge':
      return (
        <>
          <span className="icon">🏆</span>
          <div>
            <strong>Challenge Completed!</strong>
            <p>{notification.data.challenge.name}</p>
          </div>
        </>
      );
      
    default:
      return null;
  }
};
```

---

## 🚀 Development Best Practices

### 1. Code Organization

**Backend Structure**:
```
backend/
├── config/           # Strapi configuration
├── database/         # Database migrations
├── public/          # Static files
├── src/
│   ├── admin/       # Admin customization
│   ├── api/         # API endpoints
│   │   ├── [model]/
│   │   │   ├── content-types/
│   │   │   ├── controllers/
│   │   │   ├── routes/
│   │   │   └── services/
│   ├── components/  # Reusable components
│   ├── extensions/  # Core extensions
│   ├── middlewares/ # Custom middlewares
│   ├── plugins/     # Custom plugins
│   └── policies/    # Custom policies
└── types/           # TypeScript definitions
```

### 2. Testing Strategy

**Backend Testing**:
```typescript
// backend/tests/api/poll/poll.test.ts
describe('Poll API', () => {
  it('should require geographic zones on creation', async () => {
    const response = await request(strapi.server)
      .post('/api/polls')
      .send({
        title: 'Test Poll',
        description: 'Test',
        // Missing targetZones
      })
      .expect(400);
    
    expect(response.body.error.message).toContain('targetZones');
  });
  
  it('should validate user geographic access', async () => {
    const user = await createTestUser({ zones: ['zone1'] });
    const poll = await createTestPoll({ targetZones: ['zone2'] });
    
    const response = await request(strapi.server)
      .get(`/api/polls/${poll.id}`)
      .set('Authorization', `Bearer ${user.jwt}`)
      .expect(403);
    
    expect(response.body.error.message).toContain('Geographic restriction');
  });
});
```

### 3. Performance Optimization

**Database Indexes**:
```sql
-- Geographic queries
CREATE INDEX idx_user_zones ON users_zones_links(user_id, zone_id);
CREATE INDEX idx_poll_zones ON polls_zones_links(poll_id, zone_id);
CREATE INDEX idx_response_session_user ON response_sessions(user_id, created_at);

-- Gamification queries
CREATE INDEX idx_user_points ON users(points DESC, level DESC);
CREATE INDEX idx_points_log_user ON points_logs(user_id, created_at);
CREATE INDEX idx_achievement_user ON achievements(user_id, earned_at);

-- Analytics queries
CREATE INDEX idx_response_poll ON responses(poll_id, created_at);
CREATE INDEX idx_session_completed ON response_sessions(poll_id, completed_at);
```

### 4. Security Considerations

**Input Validation**:
```typescript
// backend/src/api/poll/controllers/poll.ts
export default {
  async create(ctx) {
    const { targetZones, ...data } = ctx.request.body;
    
    // Validate geographic zones
    if (!targetZones || targetZones.length === 0) {
      return ctx.badRequest('At least one target zone is required');
    }
    
    // Verify zones exist
    const zones = await strapi.db.query('api::geographic-zone.geographic-zone')
      .findMany({
        where: { id: { $in: targetZones } },
      });
    
    if (zones.length !== targetZones.length) {
      return ctx.badRequest('Invalid geographic zones');
    }
    
    // Create poll
    const poll = await strapi.service('api::poll.poll').create({
      data: {
        ...data,
        targetZones,
        creator: ctx.state.user.id,
      },
    });
    
    return poll;
  },
};
```

---

## 📊 Development Metrics & Milestones

### Phase 0 (Foundation)
- ✅ Strapi v5 setup with TypeScript
- ✅ GDPR plugin implementation
- ✅ Security middleware
- ✅ Audit system
- ✅ 80%+ test coverage

### Phase 1 (Core Features)
- ✅ Geographic data model
- ✅ SSO integration
- ✅ 5 question types
- ✅ Real-time analytics
- ✅ < 200ms API response time

### Phase 2 (Enhancement)
- ✅ 20 question types
- ✅ REST API v1
- ✅ Webhook system
- ✅ Multi-channel notifications
- ✅ 10 req/s rate limiting

### Phase 3 (Gamification)
- ✅ Points & levels system
- ✅ 11+ badges
- ✅ Real-time updates
- ✅ Leaderboards
- ✅ < 100ms Socket.IO latency

---

*This development plan provides a complete implementation roadmap for CivicPoll using Strapi CMS v5, focusing on code structure, implementation details, and best practices for each phase.*
# CivicPoll - Phase 0 Development Plan

**Objective:** To establish a secure, scalable, and production-ready technical foundation for the CivicPoll platform. This phase focuses on infrastructure, security, compliance, and the initial setup of the core applications using modern technologies.

**Core Technologies:**
*   **Backend:** Strapi v5
*   **Frontend:** Next.js 15 (React)
*   **Database:** PostgreSQL 14+ with PostGIS extension
*   **Deployment:** Nginx, PM2
*   **CI/CD:** GitLab CI
*   **Monitoring:** Prometheus, Grafana
*   **Security:** Certbot (SSL), ModSecurity (WAF)

---

## 1. Key Deliverables

*   A fully configured and secured production server environment.
*   A running Strapi v5 instance connected to a secure PostgreSQL database.
*   A basic Next.js 15 frontend application displaying a "coming soon" or maintenance page.
*   A complete CI/CD pipeline for automated testing, building, and deployment of both backend and frontend.
*   A robust monitoring, alerting, and backup system.
*   Full GDPR compliance for data handling, export, and deletion.
*   Comprehensive technical documentation for the foundational architecture.

---

## 2. Detailed Development Steps

### Step 1: Infrastructure & Environment Setup
*   **Task:** Provision and configure the Ubuntu 22.04 LTS server.
*   **Details:**
    *   Install prerequisites: Node.js (v18+), npm, PostgreSQL (v14+), PostGIS, Nginx, Git, Redis.
    *   Establish the defined directory structure (`/opt/civicpoll/`, `/logs/`, etc.).
*   **Reference:** `PHASE_0/01_TECHNICAL_IMPLEMENTATION.md`
*   **Acceptance Criteria:** All software installed, services running, and directory structure created.

### Step 2: Security & Compliance Foundation
*   **Task:** Implement all core security and GDPR measures.
*   **Details:**
    *   Generate and configure SSL/TLS certificates using Certbot for `civicpoll.fr.smatflow.xyz`.
    *   Implement a secure Nginx configuration with hardened headers (HSTS, CSP, X-Frame-Options).
    *   Install and configure ModSecurity WAF with OWASP Core Rule Set.
    *   Secure PostgreSQL: enable SSL, configure `pg_hba.conf` for SCRAM-SHA-256 authentication.
*   **Reference:** `PHASE_0/01_TECHNICAL_IMPLEMENTATION.md`, `PHASE_0/02_STRAPI_ADMIN_GUIDE.md`
*   **Acceptance Criteria:** SSL Labs A+ rating, security headers verified, WAF active, database connections encrypted.

### Step 3: Strapi v5 Backend Setup
*   **Task:** Initialize and configure the Strapi v5 application.
*   **Details:**
    *   Create a new Strapi v5 project using `npx create-strapi-app@latest`.
    *   Configure the database connection for PostgreSQL with PostGIS.
    *   Set up `.env` with secure, generated keys (`APP_KEYS`, `API_TOKEN_SALT`, `ADMIN_JWT_SECRET`, `JWT_SECRET`), database credentials, and email provider settings.
    *   Configure Strapi middlewares for security (`strapi::security`) with a strict Content Security Policy.
    *   Install and configure essential plugins: `pg`, `@strapi/provider-email-sendgrid`, `strapi-plugin-audit-log`.
*   **Reference:** `PHASE_0/01_TECHNICAL_IMPLEMENTATION.md`
*   **Acceptance Criteria:** Strapi application runs, connects to the database, and serves basic API requests. Initial Super Admin can be created as per the admin guide.

### Step 4: Next.js 15 Frontend Setup
*   **Task:** Initialize the Next.js 15 frontend application.
*   **Details:**
    *   Create a new Next.js 15 project using `npx create-next-app@latest`.
    *   Establish a basic project structure (pages, components, styles).
    *   Implement a static "coming soon" page as described in the `03_END_USER_GUIDE.md`.
    *   Set up environment variables to connect to the Strapi backend API.
    *   Configure a basic layout and theme consistent with the SMATFLOW brand.
*   **Reference:** `PHASE_0/03_END_USER_GUIDE.md`
*   **Acceptance Criteria:** The Next.js application runs, builds successfully, and displays the static landing page.

### Step 5: GDPR & Auditing Implementation
*   **Task:** Develop custom plugins and configurations for GDPR and auditing.
*   **Details:**
    *   **Custom GDPR Plugin:** Develop server-side logic for user data export (JSON format) and anonymization/deletion upon request.
    *   **Audit Trail:** Implement a custom service or extend the `strapi-plugin-audit-log` to log critical events (logins, data modifications, exports, permission changes) to a dedicated `audit-log` collection type.
*   **Reference:** `PHASE_0/01_TECHNICAL_IMPLEMENTATION.md`
*   **Acceptance Criteria:** API endpoints for GDPR actions are functional. Critical admin and user actions are logged in the database.

### Step 6: CI/CD Pipeline with GitLab CI
*   **Task:** Create a comprehensive CI/CD pipeline for both backend and frontend.
*   **Details:**
    *   Develop a `.gitlab-ci.yml` file with distinct stages: `test`, `build`, `deploy`.
    *   **Backend Pipeline:**
        *   `test`: Run unit and integration tests against a test database. Run security scans (`npm audit`).
        *   `build`: Create a production build of Strapi.
        *   `deploy`: Deploy to the production server via SSH, install dependencies, run migrations, and restart the PM2 process.
    *   **Frontend Pipeline:**
        *   `test`: Run linting and unit tests.
        *   `build`: Create a production build of the Next.js app.
        *   `deploy`: Deploy the static/server-side assets to the production server and restart the relevant service.
*   **Reference:** `PHASE_0/01_TECHNICAL_IMPLEMENTATION.md`
*   **Acceptance Criteria:** Pushes to the `main` branch automatically trigger a full test, build, and deployment cycle.

### Step 7: Monitoring, Alerting, and Logging
*   **Task:** Implement a full-stack monitoring and alerting system.
*   **Details:**
    *   Configure Prometheus to scrape metrics from Strapi, the Node.js process (node_exporter), and PostgreSQL (postgres_exporter).
    *   Create Grafana dashboards to visualize key metrics: API performance (latency, error rates), system resources (CPU, RAM), and GDPR metrics.
    *   Set up alerting rules in Alertmanager for critical events (e.g., high error rate, database down, high CPU usage) with notifications to the team.
*   **Reference:** `PHASE_0/01_TECHNICAL_IMPLEMENTATION.md`
*   **Acceptance Criteria:** Dashboards display real-time data. Alerts are triggered and received for defined critical events.

### Step 8: Backup & Recovery Strategy
*   **Task:** Implement and automate a reliable backup strategy.
*   **Details:**
    *   Create a shell script (`/opt/civicpoll/scripts/backup.sh`) to perform daily backups of the PostgreSQL database (`pg_dump`) and Strapi media files (`tar`).
    *   Configure a cron job to execute the backup script nightly.
    *   Implement a 30-day retention policy for local backups.
    *   (Optional but recommended) Configure synchronization of backups to a remote S3-compatible storage bucket.
*   **Reference:** `PHASE_0/01_TECHNICAL_IMPLEMENTATION.md`
*   **Acceptance Criteria:** Backups are created daily. A backup can be successfully restored to a staging environment.

---

## 3. Validation & Handoff

A full validation checklist must be completed before proceeding to Phase 1.

*   **Security Validation:**
    *   [ ] SSL Labs score is A+.
    *   [ ] All security headers are present and correct.
    *   [ ] WAF is confirmed to be blocking test attacks.
    *   [ ] Penetration test (e.g., OWASP ZAP) shows no critical vulnerabilities.
*   **Functional Validation:**
    *   [ ] Strapi admin is accessible, and the Super Admin is configured with 2FA.
    *   [ ] Next.js frontend is live on the production URL.
    *   [ ] CI/CD pipeline successfully completes a full run.
*   **Compliance Validation:**
    *   [ ] GDPR data export and deletion functionalities are tested and working.
    *   [ ] Audit logs correctly capture all specified events.
*   **Operational Validation:**
    *   [ ] Monitoring dashboards are populated with data.
    *   [ ] A test alert is successfully triggered and received.
    *   [ ] A full backup and restore cycle is successfully tested.
*   **Documentation:**
    *   [ ] All ADRs for key decisions are written.
    *   [ ] A high-level architecture diagram is created.
    *   [ ] The Strapi Admin Guide (`02_STRAPI_ADMIN_GUIDE.md`) is verified and complete.

# Multi-stage Dockerfile for Strapi v5 Backend
FROM node:20-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++ \
    curl

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json package-lock.json* pnpm-lock.yaml* ./

# Install pnpm
RUN npm install -g pnpm

# Development stage
FROM base AS development

# Install all dependencies
RUN pnpm install

# Copy source code
COPY . .

# Expose port
EXPOSE 1337

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:1337/_health || exit 1

# Start development server
CMD ["pnpm", "run", "develop"]

# Production dependencies stage
FROM base AS deps

# Install only production dependencies
RUN pnpm install --prod --frozen-lockfile

# Production build stage
FROM base AS builder

# Install all dependencies for building
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN pnpm run build

# Production stage
FROM node:20-alpine AS production

# Install system dependencies for production
RUN apk add --no-cache \
    libc6-compat \
    curl \
    dumb-init

# Create non-root user
RUN addgroup --system --gid 1001 strapi && \
    adduser --system --uid 1001 strapi

# Set working directory
WORKDIR /app

# Copy production dependencies
COPY --from=deps --chown=strapi:strapi /app/node_modules ./node_modules

# Copy built application
COPY --from=builder --chown=strapi:strapi /app/dist ./dist
COPY --from=builder --chown=strapi:strapi /app/build ./build
COPY --from=builder --chown=strapi:strapi /app/public ./public
COPY --from=builder --chown=strapi:strapi /app/package.json ./package.json

# Create uploads directory
RUN mkdir -p /app/public/uploads && \
    chown -R strapi:strapi /app/public/uploads

# Switch to non-root user
USER strapi

# Expose port
EXPOSE 1337

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:1337/_health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start production server
CMD ["node", "dist/src/index.js"]

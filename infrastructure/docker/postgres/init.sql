-- PostgreSQL initialization script for <PERSON><PERSON><PERSON>
-- This script sets up the database with PostGIS extension and security configurations

-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;
CREATE EXTENSION IF NOT EXISTS postgis_tiger_geocoder;

-- Enable pgcrypto for encryption functions
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Enable uuid-ossp for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create additional schemas for organization
CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS gdpr;
CREATE SCHEMA IF NOT EXISTS geo;

-- Grant permissions to civicpoll_user
GRANT USAGE ON SCHEMA audit TO civicpoll_user;
GRANT USAGE ON SCHEMA gdpr TO civicpoll_user;
GRANT USAGE ON SCHEMA geo TO civicpoll_user;

GRANT CREATE ON SCHEMA audit TO civicpoll_user;
GRANT CREATE ON SCHEMA gdpr TO civicpoll_user;
GRANT CREATE ON SCHEMA geo TO civicpoll_user;

-- Create audit log table
CREATE TABLE IF NOT EXISTS audit.audit_logs (
    id SERIAL PRIMARY KEY,
    action VARCHAR(255) NOT NULL,
    user_id INTEGER,
    ip_address INET,
    user_agent TEXT,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create GDPR requests table
CREATE TABLE IF NOT EXISTS gdpr.gdpr_requests (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    request_type VARCHAR(50) NOT NULL CHECK (request_type IN ('export', 'delete')),
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    data JSONB,
    notes TEXT
);

-- Create geographic zones table for France
CREATE TABLE IF NOT EXISTS geo.geographic_zones (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50),
    type VARCHAR(50) NOT NULL CHECK (type IN ('country', 'region', 'department', 'city', 'district')),
    parent_id INTEGER REFERENCES geo.geographic_zones(id),
    geometry GEOMETRY(MULTIPOLYGON, 4326),
    population INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create spatial index for geographic zones
CREATE INDEX IF NOT EXISTS idx_geographic_zones_geometry ON geo.geographic_zones USING GIST (geometry);
CREATE INDEX IF NOT EXISTS idx_geographic_zones_type ON geo.geographic_zones (type);
CREATE INDEX IF NOT EXISTS idx_geographic_zones_parent ON geo.geographic_zones (parent_id);

-- Insert basic geographic data for France
INSERT INTO geo.geographic_zones (name, code, type, geometry) VALUES 
('France', 'FR', 'country', NULL)
ON CONFLICT DO NOTHING;

-- Grant permissions on new tables
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA audit TO civicpoll_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA gdpr TO civicpoll_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA geo TO civicpoll_user;

GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA audit TO civicpoll_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA gdpr TO civicpoll_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA geo TO civicpoll_user;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for geographic_zones
CREATE TRIGGER update_geographic_zones_updated_at 
    BEFORE UPDATE ON geo.geographic_zones 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function for audit logging
CREATE OR REPLACE FUNCTION log_audit_event(
    p_action VARCHAR(255),
    p_user_id INTEGER DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_details JSONB DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    INSERT INTO audit.audit_logs (action, user_id, ip_address, user_agent, details)
    VALUES (p_action, p_user_id, p_ip_address, p_user_agent, p_details);
END;
$$ LANGUAGE plpgsql;

-- Set up row level security (RLS) for sensitive tables
ALTER TABLE audit.audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE gdpr.gdpr_requests ENABLE ROW LEVEL SECURITY;

-- Create policies for audit logs (admin only)
CREATE POLICY audit_logs_policy ON audit.audit_logs
    FOR ALL TO civicpoll_user
    USING (true);

-- Create policies for GDPR requests (users can only see their own)
CREATE POLICY gdpr_requests_policy ON gdpr.gdpr_requests
    FOR ALL TO civicpoll_user
    USING (true);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit.audit_logs (user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit.audit_logs (created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit.audit_logs (action);

CREATE INDEX IF NOT EXISTS idx_gdpr_requests_user_id ON gdpr.gdpr_requests (user_id);
CREATE INDEX IF NOT EXISTS idx_gdpr_requests_status ON gdpr.gdpr_requests (status);
CREATE INDEX IF NOT EXISTS idx_gdpr_requests_type ON gdpr.gdpr_requests (request_type);

-- Log the initialization
SELECT log_audit_event('database_initialized', NULL, NULL, 'PostgreSQL Init Script', '{"version": "1.0", "extensions": ["postgis", "pgcrypto", "uuid-ossp"]}'::jsonb);

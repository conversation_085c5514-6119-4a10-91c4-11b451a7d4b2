version: '3.8'

services:
  # PostgreSQL Database with PostGIS
  postgres:
    image: postgis/postgis:14-3.2
    container_name: civicpoll-postgres
    environment:
      POSTGRES_DB: civicpoll_fr
      POSTGRES_USER: civicpoll_user
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - civicpoll-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U civicpoll_user -d civicpoll_fr"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: civicpoll-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - civicpoll-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Strapi Backend
  backend:
    build:
      context: ../../apps/backend
      dockerfile: ../../infrastructure/docker/backend.Dockerfile
      target: ${NODE_ENV:-development}
    container_name: civicpoll-backend
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      HOST: 0.0.0.0
      PORT: 1337
      DATABASE_CLIENT: postgres
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: civicpoll_fr
      DATABASE_USERNAME: civicpoll_user
      DATABASE_PASSWORD: ${DATABASE_PASSWORD}
      DATABASE_SSL: false
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - ../../apps/backend:/app
      - backend_uploads:/app/public/uploads
      - /app/node_modules
    ports:
      - "1337:1337"
    networks:
      - civicpoll-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1337/_health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js Frontend
  frontend:
    build:
      context: ../../apps/frontend
      dockerfile: ../../infrastructure/docker/frontend.Dockerfile
      target: ${NODE_ENV:-development}
    container_name: civicpoll-frontend
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      NEXT_PUBLIC_STRAPI_URL: http://backend:1337
      NEXT_PUBLIC_APP_URL: https://civicpoll.fr.smatflow.xyz
    volumes:
      - ../../apps/frontend:/app
      - /app/node_modules
      - /app/.next
    ports:
      - "3000:3000"
    networks:
      - civicpoll-network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: civicpoll-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites-available:/etc/nginx/sites-available:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - civicpoll-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: civicpoll-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - civicpoll-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: civicpoll-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_SECURITY_ADMIN_USER: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3001:3000"
    networks:
      - civicpoll-network
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  backend_uploads:
  nginx_logs:
  prometheus_data:
  grafana_data:

networks:
  civicpoll-network:
    driver: bridge

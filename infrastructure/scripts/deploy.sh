#!/bin/bash

# CivicPoll Deployment Script
# This script deploys the CivicPoll application using Docker Compose

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="/home/<USER>/projects/smatflow/civicpoll"
INFRASTRUCTURE_DIR="$PROJECT_ROOT/infrastructure"
DOCKER_COMPOSE_FILE="$INFRASTRUCTURE_DIR/docker/docker-compose.yml"
ENV_FILE="$PROJECT_ROOT/.env"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please run setup.sh first."
    fi
    
    # Check if Docker Compose is available
    if ! docker compose version &> /dev/null; then
        error "Docker Compose is not available. Please install Docker Compose plugin."
    fi
    
    # Check if environment file exists
    if [[ ! -f "$ENV_FILE" ]]; then
        error "Environment file not found at $ENV_FILE. Please create it from the template."
    fi
    
    # Check if user is in docker group
    if ! groups $USER | grep -q docker; then
        error "User $USER is not in the docker group. Please run: sudo usermod -aG docker $USER and logout/login."
    fi
    
    log "Prerequisites check passed"
}

# Validate environment file
validate_environment() {
    log "Validating environment configuration..."
    
    # Source the environment file
    set -a
    source "$ENV_FILE"
    set +a
    
    # Check required variables
    required_vars=(
        "DATABASE_PASSWORD"
        "REDIS_PASSWORD"
        "APP_KEYS"
        "API_TOKEN_SALT"
        "ADMIN_JWT_SECRET"
        "TRANSFER_TOKEN_SALT"
        "ENCRYPTION_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            error "Required environment variable $var is not set"
        fi
        
        # Check if it's still the default template value
        if [[ "${!var}" == *"change_me"* ]] || [[ "${!var}" == *"generate"* ]]; then
            error "Environment variable $var still contains template value. Please update it."
        fi
    done
    
    log "Environment validation passed"
}

# Build Docker images
build_images() {
    log "Building Docker images..."
    
    cd "$PROJECT_ROOT"
    
    # Build backend image
    info "Building backend image..."
    docker build -f infrastructure/docker/backend.Dockerfile -t civicpoll-backend:latest apps/backend/
    
    # Build frontend image
    info "Building frontend image..."
    docker build -f infrastructure/docker/frontend.Dockerfile -t civicpoll-frontend:latest apps/frontend/
    
    log "Docker images built successfully"
}

# Start services
start_services() {
    log "Starting services with Docker Compose..."
    
    cd "$INFRASTRUCTURE_DIR/docker"
    
    # Copy environment file to docker directory
    cp "$ENV_FILE" .env
    
    # Start services
    docker compose up -d
    
    log "Services started successfully"
}

# Wait for services to be healthy
wait_for_services() {
    log "Waiting for services to be healthy..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        info "Health check attempt $attempt/$max_attempts"
        
        # Check if all services are healthy
        if docker compose ps --format json | jq -r '.[].Health' | grep -q "unhealthy"; then
            warn "Some services are still unhealthy, waiting..."
            sleep 10
            ((attempt++))
        else
            log "All services are healthy"
            return 0
        fi
    done
    
    error "Services failed to become healthy within the timeout period"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Wait for database to be ready
    sleep 10
    
    # Run Strapi migrations
    docker compose exec backend pnpm run strapi:migrate
    
    log "Database migrations completed"
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Copy monitoring configurations
    docker compose exec prometheus promtool check config /etc/prometheus/prometheus.yml
    
    # Restart Prometheus to reload configuration
    docker compose restart prometheus
    
    log "Monitoring setup completed"
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Check if services are responding
    local services=(
        "http://localhost:3000"
        "http://localhost:1337/_health"
        "http://localhost:9090"
    )
    
    for service in "${services[@]}"; do
        info "Checking $service..."
        if curl -f -s "$service" > /dev/null; then
            log "✓ $service is responding"
        else
            warn "✗ $service is not responding"
        fi
    done
    
    # Show service status
    info "Service status:"
    docker compose ps
    
    log "Deployment verification completed"
}

# Show deployment information
show_deployment_info() {
    log "Deployment completed successfully!"
    
    echo ""
    echo "=== CivicPoll Deployment Information ==="
    echo ""
    echo "Frontend URL: http://localhost:3000"
    echo "Backend URL: http://localhost:1337"
    echo "Admin Panel: http://localhost:1337/admin"
    echo "Prometheus: http://localhost:9090"
    echo "Grafana: http://localhost:3001"
    echo ""
    echo "=== Next Steps ==="
    echo "1. Configure your domain DNS to point to this server"
    echo "2. Generate SSL certificate: sudo certbot --nginx -d civicpoll.fr.smatflow.xyz"
    echo "3. Update Nginx configuration for production"
    echo "4. Create your first admin user in Strapi"
    echo "5. Configure monitoring alerts"
    echo ""
    echo "=== Useful Commands ==="
    echo "View logs: docker compose logs -f [service_name]"
    echo "Stop services: docker compose down"
    echo "Restart services: docker compose restart"
    echo "Update services: docker compose pull && docker compose up -d"
    echo ""
}

# Backup before deployment
backup_before_deploy() {
    if [[ -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
        log "Creating backup of existing deployment..."
        
        # Create backup directory
        local backup_dir="/opt/civicpoll/backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        
        # Backup database
        docker compose exec postgres pg_dump -U civicpoll_user civicpoll_fr | gzip > "$backup_dir/database.sql.gz"
        
        # Backup uploads
        docker compose cp backend:/app/public/uploads "$backup_dir/"
        
        log "Backup created at $backup_dir"
    fi
}

# Rollback function
rollback() {
    warn "Rolling back deployment..."
    
    cd "$INFRASTRUCTURE_DIR/docker"
    docker compose down
    
    # Restore from latest backup if available
    local latest_backup=$(ls -t /opt/civicpoll/backups/ | head -n1)
    if [[ -n "$latest_backup" ]]; then
        info "Restoring from backup: $latest_backup"
        # Add rollback logic here
    fi
    
    error "Deployment rolled back"
}

# Main deployment function
main() {
    local command="${1:-deploy}"
    
    case "$command" in
        "deploy")
            log "Starting CivicPoll deployment..."
            check_prerequisites
            validate_environment
            backup_before_deploy
            build_images
            start_services
            wait_for_services
            run_migrations
            setup_monitoring
            verify_deployment
            show_deployment_info
            ;;
        "rollback")
            rollback
            ;;
        "status")
            cd "$INFRASTRUCTURE_DIR/docker"
            docker compose ps
            ;;
        "logs")
            cd "$INFRASTRUCTURE_DIR/docker"
            docker compose logs -f "${2:-}"
            ;;
        "stop")
            cd "$INFRASTRUCTURE_DIR/docker"
            docker compose down
            ;;
        "restart")
            cd "$INFRASTRUCTURE_DIR/docker"
            docker compose restart "${2:-}"
            ;;
        *)
            echo "Usage: $0 {deploy|rollback|status|logs|stop|restart}"
            echo ""
            echo "Commands:"
            echo "  deploy   - Deploy the application"
            echo "  rollback - Rollback to previous version"
            echo "  status   - Show service status"
            echo "  logs     - Show service logs"
            echo "  stop     - Stop all services"
            echo "  restart  - Restart services"
            exit 1
            ;;
    esac
}

# Handle script interruption
trap rollback INT TERM

# Run main function
main "$@"

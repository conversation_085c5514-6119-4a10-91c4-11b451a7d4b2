#!/bin/bash

# CivicPoll Infrastructure Setup Script
# This script sets up the complete infrastructure for CivicPoll Phase 0

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root. Please run as a regular user with sudo privileges."
    fi
}

# Check Ubuntu version
check_ubuntu_version() {
    if ! grep -q "Ubuntu 22.04" /etc/os-release; then
        warn "This script is designed for Ubuntu 22.04 LTS. Proceeding anyway..."
    fi
}

# Update system packages
update_system() {
    log "Updating system packages..."
    sudo apt update && sudo apt upgrade -y
    sudo apt install -y curl wget gnupg2 software-properties-common apt-transport-https ca-certificates
}

# Install Node.js 20
install_nodejs() {
    log "Installing Node.js 20..."
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt install -y nodejs
    
    # Install pnpm
    log "Installing pnpm..."
    curl -fsSL https://get.pnpm.io/install.sh | sh -
    source ~/.bashrc
    
    # Verify installation
    node --version
    npm --version
    pnpm --version
}

# Install PostgreSQL with PostGIS
install_postgresql() {
    log "Installing PostgreSQL with PostGIS..."
    sudo apt install -y postgresql postgresql-contrib postgis postgresql-14-postgis-3
    
    # Start and enable PostgreSQL
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    # Create database and user
    log "Setting up PostgreSQL database..."
    sudo -u postgres psql -c "CREATE DATABASE civicpoll_fr WITH ENCODING 'UTF8';"
    sudo -u postgres psql -c "CREATE USER civicpoll_user WITH ENCRYPTED PASSWORD 'secure_password_change_me';"
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE civicpoll_fr TO civicpoll_user;"
    sudo -u postgres psql -d civicpoll_fr -c "CREATE EXTENSION postgis;"
    sudo -u postgres psql -d civicpoll_fr -c "CREATE EXTENSION pgcrypto;"
    
    log "PostgreSQL setup completed"
}

# Install Redis
install_redis() {
    log "Installing Redis..."
    sudo apt install -y redis-server
    
    # Configure Redis
    sudo sed -i 's/^# requirepass foobared/requirepass secure_redis_password_change_me/' /etc/redis/redis.conf
    sudo sed -i 's/^bind 127.0.0.1 ::1/bind 127.0.0.1/' /etc/redis/redis.conf
    
    # Start and enable Redis
    sudo systemctl restart redis-server
    sudo systemctl enable redis-server
    
    log "Redis setup completed"
}

# Install Nginx
install_nginx() {
    log "Installing Nginx..."
    sudo apt install -y nginx
    
    # Start and enable Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    # Create basic configuration
    sudo mkdir -p /etc/nginx/sites-available
    sudo mkdir -p /etc/nginx/ssl
    
    log "Nginx setup completed"
}

# Install Docker and Docker Compose
install_docker() {
    log "Installing Docker..."
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker
    sudo apt update
    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # Add user to docker group
    sudo usermod -aG docker $USER
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    log "Docker setup completed"
}

# Install SSL certificate with Certbot
install_ssl() {
    log "Installing Certbot for SSL certificates..."
    sudo snap install --classic certbot
    sudo ln -sf /snap/bin/certbot /usr/bin/certbot
    
    warn "SSL certificate generation requires domain to be pointing to this server"
    warn "Run 'sudo certbot --nginx -d civicpoll.fr.smatflow.xyz' after DNS is configured"
    
    log "Certbot setup completed"
}

# Setup firewall
setup_firewall() {
    log "Setting up UFW firewall..."
    sudo ufw --force reset
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow HTTP and HTTPS
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    
    # Allow specific ports for development
    sudo ufw allow 3000/tcp comment "Next.js development"
    sudo ufw allow 1337/tcp comment "Strapi development"
    
    # Enable firewall
    sudo ufw --force enable
    
    log "Firewall setup completed"
}

# Create application directories
create_directories() {
    log "Creating application directories..."
    sudo mkdir -p /opt/civicpoll/{app,backups,logs,ssl,scripts,monitoring}
    sudo chown -R $USER:$USER /opt/civicpoll
    
    log "Directories created"
}

# Install monitoring tools
install_monitoring() {
    log "Installing monitoring tools..."
    
    # Install Prometheus
    wget https://github.com/prometheus/prometheus/releases/download/v2.40.0/prometheus-2.40.0.linux-amd64.tar.gz
    tar xvfz prometheus-*.tar.gz
    sudo mv prometheus-*/prometheus /usr/local/bin/
    sudo mv prometheus-*/promtool /usr/local/bin/
    sudo mkdir -p /etc/prometheus /var/lib/prometheus
    sudo chown -R $USER:$USER /etc/prometheus /var/lib/prometheus
    rm -rf prometheus-*
    
    # Install Node Exporter
    wget https://github.com/prometheus/node_exporter/releases/download/v1.5.0/node_exporter-1.5.0.linux-amd64.tar.gz
    tar xvfz node_exporter-*.tar.gz
    sudo mv node_exporter-*/node_exporter /usr/local/bin/
    rm -rf node_exporter-*
    
    log "Monitoring tools installed"
}

# Setup systemd services
setup_services() {
    log "Setting up systemd services..."
    
    # Create Prometheus service
    sudo tee /etc/systemd/system/prometheus.service > /dev/null <<EOF
[Unit]
Description=Prometheus
Wants=network-online.target
After=network-online.target

[Service]
User=$USER
Group=$USER
Type=simple
ExecStart=/usr/local/bin/prometheus \\
    --config.file /etc/prometheus/prometheus.yml \\
    --storage.tsdb.path /var/lib/prometheus/ \\
    --web.console.templates=/etc/prometheus/consoles \\
    --web.console.libraries=/etc/prometheus/console_libraries \\
    --web.listen-address=0.0.0.0:9090 \\
    --web.enable-lifecycle

[Install]
WantedBy=multi-user.target
EOF

    # Create Node Exporter service
    sudo tee /etc/systemd/system/node_exporter.service > /dev/null <<EOF
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=$USER
Group=$USER
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd and enable services
    sudo systemctl daemon-reload
    sudo systemctl enable prometheus
    sudo systemctl enable node_exporter
    
    log "Systemd services configured"
}

# Generate environment file template
generate_env_template() {
    log "Generating environment file template..."
    
    cat > /opt/civicpoll/.env.template <<EOF
# CivicPoll Environment Configuration
# Copy this file to .env and update the values

# Database Configuration
DATABASE_PASSWORD=secure_password_change_me
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=civicpoll_fr
DATABASE_USERNAME=civicpoll_user
DATABASE_SSL=true

# Redis Configuration
REDIS_PASSWORD=secure_redis_password_change_me
REDIS_HOST=localhost
REDIS_PORT=6379

# Application Secrets (generate new ones)
APP_KEYS=generate_new_keys_here
API_TOKEN_SALT=generate_new_salt_here
ADMIN_JWT_SECRET=generate_new_secret_here
TRANSFER_TOKEN_SALT=generate_new_salt_here
ENCRYPTION_KEY=generate_new_key_here

# Email Configuration
EMAIL_PROVIDER=sendgrid
EMAIL_SENDGRID_API_KEY=your_sendgrid_api_key_here

# Monitoring
GRAFANA_PASSWORD=secure_grafana_password_change_me

# Node Environment
NODE_ENV=production
EOF

    log "Environment template created at /opt/civicpoll/.env.template"
}

# Main installation function
main() {
    log "Starting CivicPoll infrastructure setup..."
    
    check_root
    check_ubuntu_version
    
    update_system
    install_nodejs
    install_postgresql
    install_redis
    install_nginx
    install_docker
    install_ssl
    setup_firewall
    create_directories
    install_monitoring
    setup_services
    generate_env_template
    
    log "Infrastructure setup completed successfully!"
    log "Next steps:"
    log "1. Copy /opt/civicpoll/.env.template to /opt/civicpoll/.env and update values"
    log "2. Configure DNS for civicpoll.fr.smatflow.xyz"
    log "3. Generate SSL certificate: sudo certbot --nginx -d civicpoll.fr.smatflow.xyz"
    log "4. Deploy the application using docker-compose"
    
    warn "Please reboot the system to ensure all changes take effect"
}

# Run main function
main "$@"

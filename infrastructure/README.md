# Infrastructure Configuration

This directory contains all infrastructure-related configurations for the CivicPoll platform.

## Directory Structure

```
infrastructure/
├── docker/                 # Docker configurations
├── nginx/                  # Nginx configurations
├── monitoring/             # Prometheus, Grafana configs
├── scripts/                # Deployment and maintenance scripts
├── ssl/                    # SSL certificate configurations
└── backup/                 # Backup configurations
```

## Environment Setup

### Prerequisites
- Ubuntu 22.04 LTS
- Node.js 20+
- PostgreSQL 14+ with PostGIS
- Nginx
- Docker & Docker Compose
- pnpm

### Quick Start
1. Run the setup script: `./scripts/setup.sh`
2. Configure environment variables
3. Deploy with: `./scripts/deploy.sh`

## Security
All configurations follow security best practices:
- SSL/TLS encryption
- Security headers
- Rate limiting
- WAF protection
- GDPR compliance

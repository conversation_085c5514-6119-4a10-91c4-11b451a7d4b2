# Prometheus configuration for CivicPoll monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'civicpoll-monitor'
    environment: 'production'

# Load alerting rules
rule_files:
  - "alert_rules.yml"

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # CivicPoll Backend (Strapi)
  - job_name: 'civicpoll-backend'
    static_configs:
      - targets: ['backend:1337']
    scrape_interval: 15s
    metrics_path: '/api/metrics'
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # CivicPoll Frontend (Next.js)
  - job_name: 'civicpoll-frontend'
    static_configs:
      - targets: ['frontend:3000']
    scrape_interval: 15s
    metrics_path: '/api/metrics'
    scrape_timeout: 10s
    honor_labels: true

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Nginx Reverse Proxy
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node Exporter (System metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Docker containers
  - job_name: 'docker'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    scrape_timeout: 10s

  # SSL Certificate monitoring
  - job_name: 'ssl-exporter'
    static_configs:
      - targets: ['ssl-exporter:9219']
    scrape_interval: 300s  # Check every 5 minutes
    scrape_timeout: 10s
    params:
      target: ['civicpoll.fr.smatflow.xyz:443']

# Remote write configuration (optional - for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 10GB

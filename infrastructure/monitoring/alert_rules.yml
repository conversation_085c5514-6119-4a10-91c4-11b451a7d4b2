# Prometheus alerting rules for CivicPoll
groups:
  - name: civicpoll.rules
    rules:
      # High error rate alert
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "High error rate detected on {{ $labels.job }}"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.job }} service"

      # High response time alert
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "High response time on {{ $labels.job }}"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}"

      # Service down alert
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "{{ $labels.job }} has been down for more than 1 minute"

      # Database connection issues
      - alert: DatabaseConnectionHigh
        expr: pg_stat_database_numbackends > 80
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "High number of database connections"
          description: "PostgreSQL has {{ $value }} active connections"

      # Database down
      - alert: DatabaseDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgres
        annotations:
          summary: "PostgreSQL database is down"
          description: "PostgreSQL database has been down for more than 1 minute"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      # High CPU usage
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      # Disk space low
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "Low disk space on {{ $labels.instance }}"
          description: "Disk space is {{ $value | humanizePercentage }} full on {{ $labels.mountpoint }}"

      # SSL certificate expiring
      - alert: SSLCertificateExpiring
        expr: ssl_cert_not_after - time() < 86400 * 30  # 30 days
        for: 1h
        labels:
          severity: warning
          service: ssl
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}"

      # Redis down
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 1 minute"

      # High Redis memory usage
      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

      # Too many failed login attempts
      - alert: HighFailedLoginAttempts
        expr: increase(strapi_auth_failed_attempts_total[5m]) > 10
        for: 1m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High number of failed login attempts"
          description: "{{ $value }} failed login attempts in the last 5 minutes"

      # GDPR request processing delay
      - alert: GDPRRequestDelay
        expr: strapi_gdpr_pending_requests > 5
        for: 30m
        labels:
          severity: warning
          service: gdpr
        annotations:
          summary: "GDPR requests processing delay"
          description: "{{ $value }} GDPR requests are pending for more than 30 minutes"

      # Backup failure
      - alert: BackupFailure
        expr: time() - backup_last_success_timestamp > 86400 * 2  # 2 days
        for: 1h
        labels:
          severity: critical
          service: backup
        annotations:
          summary: "Backup failure detected"
          description: "Last successful backup was {{ $value | humanizeDuration }} ago"

  - name: civicpoll.security
    rules:
      # DDoS attack detection
      - alert: PossibleDDoSAttack
        expr: rate(nginx_http_requests_total[1m]) > 100
        for: 2m
        labels:
          severity: critical
          service: security
        annotations:
          summary: "Possible DDoS attack detected"
          description: "Request rate is {{ $value }} requests/second"

      # Unusual geographic access pattern
      - alert: UnusualGeographicAccess
        expr: increase(nginx_http_requests_total{country!="FR"}[1h]) > 1000
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Unusual geographic access pattern"
          description: "{{ $value }} requests from outside France in the last hour"

      # WAF blocking high number of requests
      - alert: HighWAFBlocks
        expr: rate(nginx_http_requests_total{status="403"}[5m]) > 5
        for: 2m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High number of requests blocked by WAF"
          description: "WAF is blocking {{ $value }} requests/second"

# Phase 0 - Guide Administrateur Strapi : Configuration Initiale

## Introduction
Ce guide vous accompagne dans la configuration initiale de Strapi pour la plateforme CivicPoll. Cette phase établit les fondations sécurisées de votre instance.

## 1. Première Connexion à Strapi

### 1.1 Accès à l'Interface d'Administration
1. Ouvrez votre navigateur et accédez à : `https://civicpoll.fr.smatflow.xyz/admin`
2. Lors de la première connexion, vous devrez créer le compte Super Admin

### 1.2 Création du Super Admin
```
Email : <EMAIL>
Mot de passe : [Minimum 8 caractères, 1 majuscule, 1 chiffre, 1 caractère spécial]
Prénom : Admin
Nom : SMATFLOW
```

**⚠️ IMPORTANT** : Notez ces identifiants dans un gestionnaire de mots de passe sécurisé.

## 2. Configuration des Paramètres Généraux

### 2.1 Paramètres de l'Application
Naviguez vers **Settings → Application**

1. **Nom de l'application** : CivicPoll France
2. **Description** : Plateforme de sondages pour la France
3. **URL du site** : https://civicpoll.fr.smatflow.xyz
4. **Langue par défaut** : Français

### 2.2 Configuration Email
Naviguez vers **Settings → Email**

1. **Provider** : SendGrid (déjà configuré)
2. **Email par défaut** : <EMAIL>
3. **Nom d'expéditeur** : CivicPoll France
4. **Email de réponse** : <EMAIL>

Testez la configuration :
- Cliquez sur "Send test email"
- Vérifiez la réception sur votre email

## 3. Gestion des Rôles et Permissions

### 3.1 Comprendre les Rôles par Défaut
Strapi propose 3 rôles de base :

1. **Super Admin** : Accès total (vous)
2. **Editor** : Peut créer/modifier du contenu
3. **Author** : Peut créer du contenu uniquement

### 3.2 Créer des Rôles Personnalisés
Pour CivicPoll, nous allons créer des rôles spécifiques :

#### Rôle "Gestionnaire RGPD"
1. Allez dans **Settings → Roles**
2. Cliquez sur "+ Add new role"
3. **Nom** : Gestionnaire RGPD
4. **Description** : Responsable de la conformité des données
5. **Permissions** :
   - ✅ Audit logs : Read, Export
   - ✅ Users : Read, Export, Delete
   - ✅ GDPR requests : All
   - ❌ Content creation : None

#### Rôle "Responsable Sécurité"
1. **Nom** : Responsable Sécurité
2. **Description** : Supervision de la sécurité
3. **Permissions** :
   - ✅ Audit logs : Read
   - ✅ Security settings : All
   - ✅ API tokens : Manage
   - ✅ Webhooks : Manage

## 4. Configuration de la Sécurité

### 4.1 Authentification à Deux Facteurs (2FA)
1. Naviguez vers **Settings → Security**
2. Activez "Two-factor authentication"
3. **Obligatoire pour** : Super Admin, Gestionnaire RGPD, Responsable Sécurité

### 4.2 Politique de Mots de Passe
Configurez les règles minimales :
- Longueur minimale : 12 caractères
- Complexité : Majuscules + Minuscules + Chiffres + Caractères spéciaux
- Expiration : 90 jours
- Historique : 5 derniers mots de passe

### 4.3 Sessions et Timeouts
- Durée de session : 8 heures
- Timeout d'inactivité : 30 minutes
- Déconnexion automatique : Activée

## 5. Configuration des Plugins

### 5.1 Plugin Audit Log
Le plugin d'audit est pré-installé. Pour le configurer :

1. Allez dans **Plugins → Audit Log**
2. Activez les événements à tracer :
   - ✅ Connexions/Déconnexions
   - ✅ Modifications de données
   - ✅ Exports de données
   - ✅ Suppressions
   - ✅ Changements de permissions

3. **Rétention des logs** : 365 jours (RGPD)

### 5.2 Plugin RGPD
1. Naviguez vers **Plugins → GDPR**
2. Configuration :
   - **Anonymisation automatique** : Activée
   - **Délai avant suppression** : 30 jours
   - **Export format** : JSON et CSV
   - **Chiffrement des exports** : Activé

### 5.3 Plugin SSO (pour Phase 1)
1. **Status** : Installé mais non configuré
2. Note : "Configuration lors de la Phase 1"

## 6. Gestion des Médias

### 6.1 Configuration du Stockage
1. Allez dans **Settings → Media Library**
2. **Provider** : Local (Phase 0)
3. **Taille max fichier** : 10 MB
4. **Types autorisés** : Images uniquement (.jpg, .png, .webp)
5. **Optimisation** : Activée (compression automatique)

### 6.2 Organisation des Médias
Créez la structure de dossiers :
```
/media
  /logos         # Logos organisations
  /avatars       # Photos profils
  /sondages      # Images pour sondages
  /système       # Assets système
```

## 7. Configuration des Webhooks

### 7.1 Webhook de Monitoring
1. **Settings → Webhooks → Add webhook**
2. **Nom** : Health Check Monitor
3. **URL** : https://monitoring.smatflow.xyz/health
4. **Events** : 
   - Server started
   - Server stopped
   - Database connection lost

### 7.2 Webhook RGPD
1. **Nom** : GDPR Compliance
2. **URL** : https://rgpd.smatflow.xyz/events
3. **Events** :
   - User data exported
   - User data deleted
   - Consent updated

## 8. Backup et Maintenance

### 8.1 Vérifier les Backups Automatiques
1. Allez dans **Settings → Backups**
2. Vérifiez :
   - ✅ Backup quotidien : 3h00
   - ✅ Rétention : 30 jours
   - ✅ Destination : Local + S3

### 8.2 Planifier la Maintenance
1. **Fenêtre de maintenance** : Dimanche 2h-5h
2. **Notifications** : 48h à l'avance
3. **Page de maintenance** : Activée automatiquement

## 9. Monitoring et Tableaux de Bord

### 9.1 Dashboard Principal
Votre dashboard affiche :
- État du système (CPU, RAM, Disque)
- Dernières connexions
- Activité récente
- Alertes de sécurité

### 9.2 Métriques Clés
Surveillez quotidiennement :
- **Uptime** : Doit être > 99.9%
- **Temps de réponse** : < 200ms
- **Erreurs** : < 0.1%
- **Tentatives de connexion échouées** : Analyser les patterns

## 10. Documentation et Support

### 10.1 Documentation Interne
Créez et maintenez :
1. **Procédures d'urgence** : En cas de problème
2. **Contacts clés** : Équipe technique, sécurité
3. **Changelog** : Toutes les modifications

### 10.2 Formation de l'Équipe
Assurez-vous que :
- Tous les admins connaissent les procédures RGPD
- La 2FA est activée pour tous
- Les rôles sont correctement attribués

## 11. Checklist de Validation Admin

### Configuration Système ✓
- [ ] Super Admin créé avec mot de passe fort
- [ ] 2FA activée pour le Super Admin
- [ ] Email configuré et testé
- [ ] Langue française définie

### Sécurité ✓
- [ ] Politique de mots de passe configurée
- [ ] Timeouts de session définis
- [ ] Audit log activé
- [ ] Webhooks de monitoring en place

### RGPD ✓
- [ ] Plugin RGPD configuré
- [ ] Processus d'anonymisation actif
- [ ] Exports de données testés
- [ ] Documentation des traitements

### Rôles et Permissions ✓
- [ ] Rôles personnalisés créés
- [ ] Permissions vérifiées
- [ ] Au moins 2 admins configurés

### Backup et Monitoring ✓
- [ ] Backups automatiques vérifiés
- [ ] Alertes configurées
- [ ] Dashboard de monitoring accessible

## 12. Prochaines Étapes

Une fois cette configuration initiale terminée :

1. **Effectuez un test de restauration** depuis un backup
2. **Documentez toutes les configurations** spécifiques
3. **Formez un administrateur de secours**
4. **Planifiez une revue de sécurité** mensuelle

---

## ⚠️ Points d'Attention

1. **Ne jamais désactiver** l'audit log ou la 2FA
2. **Toujours tester** les modifications en environnement de test
3. **Documenter** tous les changements de configuration
4. **Sauvegarder** avant toute mise à jour majeure

## 📞 Support

En cas de problème :
- **Support Technique** : <EMAIL>
- **Urgence Sécurité** : <EMAIL>
- **Documentation** : https://docs.civicpoll.fr

---

**✅ Une fois toutes ces étapes complétées, votre instance Strapi est prête et sécurisée pour accueillir les développements de la Phase 1.**

---

*Guide Administrateur Strapi - Phase 0 - Version 1.0*
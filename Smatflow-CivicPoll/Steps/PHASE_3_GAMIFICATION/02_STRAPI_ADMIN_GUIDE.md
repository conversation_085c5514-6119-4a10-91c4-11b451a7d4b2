# Phase 2 - Guide Administrateur Strapi : Gestion de la Gamification

## Introduction
Cette phase ajoute tout le système de gamification à CivicPoll. Vous allez apprendre à configurer et gérer les points, badges, défis et classements pour maximiser l'engagement des utilisateurs.

## 1. Vue d'Ensemble Gamification

### 1.1 Nouveaux Modules Disponibles
Dans le menu Strapi, vous avez maintenant :
- **User Profiles** : Profils gamifiés des utilisateurs
- **Badges** : Gestion des badges et récompenses
- **Achievements** : Historique des réalisations
- **Challenges** : Défis quotidiens/hebdomadaires/mensuels
- **Leaderboards** : Classements automatiques

### 1.2 Dashboard Gamification
Un nouveau widget sur votre dashboard affiche :
```
🎮 Métriques Gamification Aujourd'hui
├── Badges distribués : 127
├── Points totaux gagnés : 3,450
├── Défis complétés : 89
├── Nouveaux niveaux atteints : 12
└── Taux d'engagement : +23% ↑
```

## 2. Configuration des Badges

### 2.1 Types de Badges Prédéfinis

#### Badges de Participation
- **Premier Pas** : 1er sondage (50 pts)
- **Habitué** : 10 sondages (100 pts)
- **Expert** : 50 sondages (500 pts)
- **Maître** : 100 sondages (1000 pts)

#### Badges de Régularité
- **Régulier** : 7 jours consécutifs (200 pts)
- **Assidu** : 14 jours consécutifs (400 pts)
- **Dévoué** : 30 jours consécutifs (1000 pts)

#### Badges Géographiques
- **Citoyen Local** : 5 sondages ville (150 pts)
- **Ambassadeur Départemental** : 20 sondages département (300 pts)
- **Explorateur Régional** : Sondages dans 3 départements (500 pts)

#### Badges Spéciaux
- **Pionnier** : 100 premiers utilisateurs (500 pts) 
- **Nocturne** : Participation entre 0h-6h (100 pts)
- **Week-end Actif** : 5 participations weekend (200 pts)

### 2.2 Créer un Nouveau Badge

1. **Content Manager → Badges → "+ Add new entry"**

2. **Informations de base** :
   - **Name** : Nom unique et évocateur
   - **Description** : Explication claire de l'obtention
   - **Category** : Type de badge
   - **Points value** : Points attribués (10-1000)
   - **Rarity** : 
     - Common (vert) : Facile à obtenir
     - Uncommon (bleu) : Effort modéré
     - Rare (violet) : Difficile
     - Epic (orange) : Très difficile
     - Legendary (doré) : Exceptionnel

3. **Icône** :
   - Format : PNG transparent 256x256
   - Style cohérent avec les autres
   - Couleurs selon la rareté

4. **Critères (JSON)** :
```json
{
  "type": "poll_count",
  "value": 25,
  "conditions": {
    "zone_type": "city",
    "time_limit_days": 30
  }
}
```

### 2.3 Types de Critères Disponibles

**Basés sur la quantité** :
```json
{
  "type": "poll_count",
  "value": 10
}
```

**Basés sur la régularité** :
```json
{
  "type": "streak",
  "value": 7
}
```

**Basés sur la géographie** :
```json
{
  "type": "geographic",
  "scope": "department",
  "count": 5
}
```

**Basés sur la qualité** :
```json
{
  "type": "quality_answers",
  "min_words": 50,
  "count": 10
}
```

**Événements spéciaux** :
```json
{
  "type": "special_event",
  "event": "first_poll_created",
  "organization_type": "municipality"
}
```

### 2.4 Gestion des Badges Existants

Pour modifier un badge :
1. **Ne jamais changer** les critères d'un badge déjà attribué
2. **Désactiver** plutôt que supprimer
3. **Créer une v2** si modification majeure nécessaire

## 3. Système de Points et Niveaux

### 3.1 Configuration des Points

**Content Manager → Settings → Gamification**

Actions et points par défaut :
- Compléter un sondage : 10 pts
- Premier sondage : 50 pts bonus
- Réponse rapide (<2 min) : 3 pts bonus
- Réponse détaillée (>50 mots) : 5 pts bonus
- Série de participations : 5 pts/jour
- Défi complété : Variable (20-100 pts)

### 3.2 Niveaux et Progression

**Barème des niveaux** :
```
Niveau 1 : 0 pts (Débutant)
Niveau 2 : 100 pts (Participant)
Niveau 3 : 300 pts (Contributeur)
Niveau 4 : 600 pts (Actif)
Niveau 5 : 1,000 pts (Engagé)
Niveau 6 : 1,500 pts (Expert)
Niveau 7 : 2,500 pts (Vétéran)
Niveau 8 : 4,000 pts (Maître)
Niveau 9 : 6,000 pts (Champion)
Niveau 10 : 10,000 pts (Légende)
```

### 3.3 Multiplicateurs et Bonus

Configurez des bonus contextuels :
- **Bonus première participation du jour** : x2
- **Bonus série 7+ jours** : x1.5
- **Bonus weekend** : x1.2
- **Bonus zone peu active** : x1.5

## 4. Gestion des Défis

### 4.1 Types de Défis

#### Défis Quotidiens (réinitialisation minuit)
- Participer à 3 sondages (50 pts)
- Réponse complète en <2 min (30 pts)
- Donner un avis détaillé (25 pts)

#### Défis Hebdomadaires
- Participer 5 jours sur 7 (100 pts)
- Explorer 3 catégories différentes (75 pts)
- Atteindre 200 points (150 pts + badge)

#### Défis Mensuels
- Maintenir une série de 20 jours (500 pts)
- Participer à 15 sondages (300 pts)
- Devenir top 10 de sa ville (badge spécial)

### 4.2 Créer un Défi

1. **Content Manager → Challenges → "+ Add new entry"**

2. **Configuration** :
```json
{
  "title": "Marathon de Participation",
  "description": "Participez à 5 sondages cette semaine",
  "type": "weekly",
  "criteria": {
    "type": "poll_count",
    "count": 5,
    "timeframe": "week"
  },
  "reward": {
    "points": 100,
    "badge": null
  },
  "start_date": "2024-02-05T00:00:00",
  "end_date": "2024-02-11T23:59:59"
}
```

3. **Ciblage géographique** (optionnel) :
   - Sélectionnez les zones concernées
   - Laissez vide pour défi national

### 4.3 Défis Automatiques vs Manuels

**Automatiques** (recommandé) :
- Générés chaque jour/semaine/mois
- Templates prédéfinis
- Rotation automatique

**Manuels** :
- Événements spéciaux
- Campagnes thématiques
- Défis sponsorisés

### 4.4 Monitoring des Défis

Dashboard défis :
```
Défi : "Explorateur Local"
├── Participants : 342/1,200 (28.5%)
├── Complétions : 89 (26%)
├── Points distribués : 1,780
└── Temps moyen : 3.2 jours
```

## 5. Classements et Leaderboards

### 5.1 Types de Classements

**Par portée géographique** :
- 🌍 Global : Tous les utilisateurs
- 🗺️ Régional : Par région
- 🏛️ Départemental : Par département
- 🏙️ Municipal : Par ville

**Par période** :
- Hebdomadaire (lun-dim)
- Mensuel (1er-dernier jour)
- Annuel
- All-time

### 5.2 Configuration des Classements

**Settings → Leaderboards**

Options :
- **Mise à jour** : Horaire (temps réel) ou Batch (1h)
- **Taille** : Top 10, 50, 100
- **Anonymisation** : Masquer noms si souhaité
- **Prizes** : Badges automatiques top 3

### 5.3 Gestion de la Compétition

**Éviter les abus** :
1. Limite de points/jour : 500
2. Détection patterns suspects
3. Validation manuelle top 10
4. Cooldown entre participations

**Encourager la participation saine** :
- Bonus diversité (différents types de sondages)
- Points qualité > quantité
- Récompenses participation régulière

## 6. Analytics Gamification

### 6.1 Métriques Clés

**Dashboard Analytics → Gamification**

KPIs principaux :
- **Taux d'engagement** : % utilisateurs actifs/gamifiés
- **Rétention J7/J30** : Impact de la gamification
- **Points moyens/utilisateur** : Progression globale
- **Distribution des niveaux** : Pyramide des joueurs
- **Badges les plus convoités** : Motivation

### 6.2 Rapports Automatiques

Configuration des rapports :
1. **Reports → Gamification Reports**
2. Fréquence : Quotidien/Hebdo/Mensuel
3. Métriques incluses
4. Destinataires email

Exemple rapport hebdomadaire :
```
📊 Rapport Gamification - Semaine 6

Engagement:
✅ Utilisateurs gamifiés : 3,456 (+12%)
✅ Points distribués : 45,670 (+23%)
✅ Badges débloqués : 234 (+18%)

Top Performers:
🥇 MarieL_75 : 1,250 pts
🥈 JeanP_69 : 1,180 pts
🥉 SophieM_13 : 1,090 pts

Défis:
- Taux complétion : 67%
- Plus populaire : "Explorateur Local"
- Points via défis : 12,300

Recommandations:
- Augmenter rewards défis weekend
- Créer badge spécial 1000 pts
- Promouvoir classement régional
```

### 6.3 A/B Testing

Testez l'impact de :
- Valeurs de points différentes
- Nouveaux types de badges
- Fréquence des défis
- Messages de motivation

## 7. Notifications et Communications

### 7.1 Notifications Automatiques

**Email → Gamification Templates**

Templates disponibles :
- 🎉 Badge débloqué
- 📈 Nouveau niveau atteint
- 🏆 Entrée dans le top 10
- 💪 Nouveau défi disponible
- 🔥 Rappel série à maintenir

### 7.2 Configuration des Triggers

```javascript
// Exemple configuration notification
{
  "trigger": "badge_earned",
  "conditions": {
    "badge_rarity": ["rare", "epic", "legendary"]
  },
  "channels": ["email", "in_app"],
  "template": "badge_earned_special"
}
```

### 7.3 Push Notifications (Phase 4)

Préparation :
- Templates courts (< 100 car)
- Call-to-action clairs
- Timing intelligent
- Respect des préférences

## 8. Modération et Fair-Play

### 8.1 Détection des Abus

**Monitoring → Suspicious Activity**

Signaux d'alerte :
- ⚠️ Participation trop rapide (<30s)
- ⚠️ Patterns répétitifs réponses
- ⚠️ Accumulation points anormale
- ⚠️ Multi-comptes même IP

### 8.2 Actions de Modération

Pour un utilisateur suspect :
1. **Review** : Examiner l'historique
2. **Warning** : Avertissement privé
3. **Penalty** : Retrait de points
4. **Suspension** : Blocage temporaire
5. **Ban** : Exclusion définitive

### 8.3 Appels et Contestations

Process :
1. L'utilisateur conteste via formulaire
2. Review par admin différent
3. Décision sous 48h
4. Restauration si erreur

## 9. Événements Spéciaux

### 9.1 Planifier un Événement

**Events → Create Special Event**

Exemples :
- **Semaine de la Démocratie** : Points x2
- **Défi National** : Badge exclusif
- **Anniversaire CivicPoll** : Récompenses spéciales

### 9.2 Configuration
```json
{
  "name": "Mois de l'Engagement Citoyen",
  "start": "2024-03-01",
  "end": "2024-03-31",
  "bonuses": {
    "points_multiplier": 1.5,
    "special_badge": "citizen_month_2024"
  },
  "challenges": ["special_challenge_ids"],
  "leaderboard": "monthly_special"
}
```

### 9.3 Communication

Checklist événement :
- [ ] Email d'annonce J-7
- [ ] Badge spécial créé
- [ ] Défis configurés
- [ ] Landing page dédiée
- [ ] Bannière in-app
- [ ] Posts réseaux sociaux

## 10. Optimisation de l'Engagement

### 10.1 Stratégies Efficaces

**Nouveaux utilisateurs** :
- Tutoriel gamification interactif
- Premier badge facile (quick win)
- Objectifs progressifs clairs

**Utilisateurs réguliers** :
- Défis variés et renouvelés
- Récompenses exclusives
- Reconnaissance publique

**Power users** :
- Défis ultra difficiles
- Badges légendaires
- Rôle de mentor

### 10.2 Calendrier Type

**Lundi** : Nouveaux défis hebdo
**Mercredi** : Rappel mi-parcours
**Vendredi** : Boost weekend annoncé
**Dimanche** : Récap et félicitations

### 10.3 Saisonnalité

Adapter selon :
- Périodes de vacances (défis famille)
- Actualités (défis thématiques)
- Saisons (challenges extérieur/intérieur)
- Événements locaux

## 11. Troubleshooting Gamification

### 11.1 Points non attribués

Vérifier :
1. Logs → Gamification Events
2. User profile → Points history
3. Badge criteria → Conditions
4. System → Queue jobs

### 11.2 Badges non débloqués

Debug :
```sql
-- Vérifier les critères
SELECT * FROM badges WHERE id = 'badge_id';
-- Vérifier l'historique user
SELECT * FROM achievements WHERE user_id = 'user_id';
```

### 11.3 Classements incorrects

Actions :
1. Force refresh : `npm run strapi leaderboard:refresh`
2. Clear cache : Redis FLUSHDB
3. Recalcul manuel si nécessaire

## 12. Checklist Admin Phase 2

### Configuration Initiale ✓
- [ ] Tous les badges importés et vérifiés
- [ ] Niveaux et points configurés
- [ ] Défis quotidiens actifs
- [ ] Classements opérationnels
- [ ] Templates notifications

### Monitoring Quotidien ✓
- [ ] Vérifier métriques engagement
- [ ] Valider top 10 classements
- [ ] Modérer activités suspectes
- [ ] Répondre aux contestations

### Maintenance Hebdomadaire ✓
- [ ] Analyser rapport gamification
- [ ] Ajuster valeurs points si nécessaire
- [ ] Planifier défis semaine suivante
- [ ] Vérifier feedback utilisateurs

### Évolution Mensuelle ✓
- [ ] Créer nouveaux badges
- [ ] Analyser impact sur rétention
- [ ] Proposer améliorations
- [ ] Planifier événements spéciaux

---

## 🎮 Best Practices Gamification

1. **Équilibre** : Ni trop facile, ni trop difficile
2. **Variété** : Différents types de défis
3. **Surprise** : Badges cachés et découvertes
4. **Social** : Valoriser le partage
5. **Progression** : Toujours un objectif suivant

---

*Guide Administrateur Strapi - Phase 2 - Version 1.0*
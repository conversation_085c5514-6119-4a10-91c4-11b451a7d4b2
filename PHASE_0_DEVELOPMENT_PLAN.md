# CivicPoll - Phase 0 Development Plan: Security & GDPR Foundation

## 🎯 Phase 0 Overview

**Duration**: 4-6 weeks  
**Objective**: Establish a secure, GDPR-compliant infrastructure foundation for CivicPoll

Phase 0 focuses exclusively on creating a bulletproof security and compliance foundation. No user-facing features are developed in this phase - it's entirely about infrastructure, security, and compliance.

### Key Deliverables
- ✅ Secure Strapi v5 installation with PostgreSQL/PostGIS
- ✅ GDPR compliance framework with full audit trail
- ✅ Security middleware and monitoring
- ✅ Automated backup and recovery system
- ✅ CI/CD pipeline with security scanning
- ✅ Complete documentation and admin training

---

## 🛠️ Week 1-2: Infrastructure Setup & Security

### 1. Strapi v5 Project Initialization

**Create Strapi Project Structure**:
```bash
# Initialize Strapi v5 with TypeScript
npx create-strapi@latest civicpoll-backend \
  --typescript \
  --no-run \
  --dbclient postgres \
  --dbhost localhost \
  --dbport 5432 \
  --dbname civicpoll_fr

# Project structure
civicpoll/
├── backend/              # Strapi v5 application
├── scripts/             # Maintenance scripts
├── config/              # External configurations
├── docker/              # Docker configurations
└── docs/                # Documentation
```

**Core Dependencies Installation**:
```json
{
  "dependencies": {
    "@strapi/strapi": "^5.0.0",
    "@strapi/plugin-users-permissions": "^5.0.0",
    "@strapi/plugin-email": "^5.0.0",
    "@strapi/provider-email-sendgrid": "^5.0.0",
    "pg": "^8.11.0",
    "bcrypt": "^5.1.0",
    "jsonwebtoken": "^9.0.0",
    "node-cron": "^3.0.0",
    "winston": "^3.11.0",
    "helmet": "^7.1.0",
    "express-rate-limit": "^7.1.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "typescript": "^5.3.0",
    "eslint": "^8.0.0",
    "jest": "^29.0.0",
    "@types/jest": "^29.0.0"
  }
}
```

### 2. Database Configuration with Security

**PostgreSQL Setup with PostGIS**:
```sql
-- Create database with proper encoding
CREATE DATABASE civicpoll_fr 
  WITH ENCODING 'UTF8' 
  LC_COLLATE = 'fr_FR.UTF-8' 
  LC_CTYPE = 'fr_FR.UTF-8';

-- Create secure user
CREATE USER civicpoll_user WITH ENCRYPTED PASSWORD 'generate_secure_password_here';
GRANT CONNECT ON DATABASE civicpoll_fr TO civicpoll_user;
GRANT USAGE ON SCHEMA public TO civicpoll_user;
GRANT CREATE ON SCHEMA public TO civicpoll_user;

-- Enable extensions
\c civicpoll_fr
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS pgcrypto;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Security configurations
ALTER DATABASE civicpoll_fr SET ssl = on;
ALTER DATABASE civicpoll_fr SET log_statement = 'all';
ALTER DATABASE civicpoll_fr SET log_connections = on;
```

**Strapi Database Configuration** (`backend/config/database.ts`):
```typescript
import { parse } from 'pg-connection-string';

export default ({ env }) => {
  const client = 'postgres';
  
  const connections = {
    postgres: {
      connection: {
        host: env('DATABASE_HOST', 'localhost'),
        port: env.int('DATABASE_PORT', 5432),
        database: env('DATABASE_NAME', 'civicpoll_fr'),
        user: env('DATABASE_USERNAME', 'civicpoll_user'),
        password: env('DATABASE_PASSWORD'),
        ssl: env.bool('DATABASE_SSL', true) && {
          rejectUnauthorized: env.bool('DATABASE_SSL_REJECT_UNAUTHORIZED', true),
        },
        schema: env('DATABASE_SCHEMA', 'public'),
      },
      pool: {
        min: env.int('DATABASE_POOL_MIN', 2),
        max: env.int('DATABASE_POOL_MAX', 10),
        acquireTimeoutMillis: env.int('DATABASE_POOL_ACQUIRE', 60000),
        createTimeoutMillis: env.int('DATABASE_POOL_CREATE', 30000),
        idleTimeoutMillis: env.int('DATABASE_POOL_IDLE', 30000),
      },
    },
  };

  return {
    connection: {
      client,
      ...connections[client],
      debug: false, // Never enable in production
    },
  };
};
```

### 3. Security Middleware Implementation

**Core Security Middleware** (`backend/src/middlewares/security.ts`):
```typescript
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { errors } from '@strapi/utils';

const { ApplicationError } = errors;

export default (config, { strapi }) => {
  return async (ctx, next) => {
    // Apply helmet security headers
    const helmetMiddleware = helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "blob:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      crossOriginEmbedderPolicy: true,
      crossOriginOpenerPolicy: true,
      crossOriginResourcePolicy: { policy: "cross-origin" },
      dnsPrefetchControl: true,
      frameguard: { action: 'deny' },
      hidePoweredBy: true,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
      ieNoOpen: true,
      noSniff: true,
      originAgentCluster: true,
      permittedCrossDomainPolicies: false,
      referrerPolicy: { policy: "no-referrer" },
      xssFilter: true,
    });

    // Apply rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // Limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    });

    // Custom security checks
    // Check for SQL injection attempts in query params
    const sqlInjectionPattern = /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|eval)\b)/gi;
    const queryString = ctx.request.url.split('?')[1];
    
    if (queryString && sqlInjectionPattern.test(decodeURIComponent(queryString))) {
      strapi.log.warn(`Potential SQL injection attempt from IP: ${ctx.request.ip}`);
      throw new ApplicationError('Invalid request parameters', 400);
    }

    // Log security events
    if (ctx.request.url.includes('/admin') || ctx.request.url.includes('/api')) {
      await strapi.service('api::audit-log.audit-log').create({
        action: 'access',
        path: ctx.request.url,
        method: ctx.request.method,
        ip: ctx.request.ip,
        userAgent: ctx.request.header['user-agent'],
        timestamp: new Date(),
      });
    }

    await next();
  };
};
```

**Request Validation Middleware** (`backend/src/middlewares/validation.ts`):
```typescript
export default (config, { strapi }) => {
  return async (ctx, next) => {
    // Validate content-type
    if (['POST', 'PUT', 'PATCH'].includes(ctx.request.method)) {
      const contentType = ctx.request.header['content-type'];
      
      if (!contentType || !contentType.includes('application/json')) {
        ctx.throw(400, 'Content-Type must be application/json');
      }
    }

    // Validate request size
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (ctx.request.length > maxSize) {
      ctx.throw(413, 'Request entity too large');
    }

    // Sanitize inputs
    if (ctx.request.body) {
      ctx.request.body = sanitizeObject(ctx.request.body);
    }

    await next();
  };
};

function sanitizeObject(obj: any): any {
  if (typeof obj !== 'object' || obj === null) {
    return sanitizeValue(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }

  const sanitized = {};
  for (const [key, value] of Object.entries(obj)) {
    // Skip keys that look suspicious
    if (!/^[a-zA-Z0-9_-]+$/.test(key)) {
      continue;
    }
    sanitized[key] = sanitizeObject(value);
  }
  return sanitized;
}

function sanitizeValue(value: any): any {
  if (typeof value === 'string') {
    // Remove any potential script tags or SQL injection attempts
    return value
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/[;'"\\]/g, '');
  }
  return value;
}
```

### 4. Audit System Implementation

**Audit Log Content Type** (`backend/src/api/audit-log/content-types/audit-log/schema.json`):
```json
{
  "kind": "collectionType",
  "collectionName": "audit_logs",
  "info": {
    "singularName": "audit-log",
    "pluralName": "audit-logs",
    "displayName": "Audit Log",
    "description": "System audit trail for compliance"
  },
  "options": {
    "draftAndPublish": false,
    "timestamps": true
  },
  "attributes": {
    "action": {
      "type": "enumeration",
      "enum": [
        "login",
        "logout",
        "create",
        "read",
        "update",
        "delete",
        "export",
        "consent_given",
        "consent_withdrawn",
        "data_request",
        "data_deletion",
        "security_alert",
        "configuration_change",
        "permission_change"
      ],
      "required": true
    },
    "entity": {
      "type": "string",
      "required": true
    },
    "entityId": {
      "type": "string"
    },
    "userId": {
      "type": "string"
    },
    "userEmail": {
      "type": "string"
    },
    "ipAddress": {
      "type": "string",
      "required": true
    },
    "userAgent": {
      "type": "text"
    },
    "metadata": {
      "type": "json"
    },
    "success": {
      "type": "boolean",
      "default": true
    },
    "errorMessage": {
      "type": "text"
    },
    "severity": {
      "type": "enumeration",
      "enum": ["low", "medium", "high", "critical"],
      "default": "low"
    }
  }
}
```

**Audit Service** (`backend/src/api/audit-log/services/audit-log.ts`):
```typescript
import crypto from 'crypto';

export default ({ strapi }) => ({
  async create(data: any) {
    // Hash sensitive data
    const hashedData = {
      ...data,
      ipAddress: this.hashIP(data.ipAddress),
      checksum: this.generateChecksum(data),
    };

    // Create immutable audit log entry
    const entry = await strapi.entityService.create('api::audit-log.audit-log', {
      data: hashedData,
    });

    // Alert on critical events
    if (data.severity === 'critical') {
      await this.alertSecurityTeam(entry);
    }

    // Archive old logs
    await this.archiveOldLogs();

    return entry;
  },

  hashIP(ip: string): string {
    // Keep first two octets, hash the rest for privacy
    const parts = ip.split('.');
    if (parts.length === 4) {
      const hash = crypto
        .createHash('sha256')
        .update(parts.slice(2).join('.'))
        .digest('hex')
        .substring(0, 8);
      return `${parts[0]}.${parts[1]}.xx.${hash}`;
    }
    return crypto.createHash('sha256').update(ip).digest('hex').substring(0, 16);
  },

  generateChecksum(data: any): string {
    const content = JSON.stringify({
      action: data.action,
      entity: data.entity,
      entityId: data.entityId,
      userId: data.userId,
      timestamp: data.timestamp,
    });
    
    return crypto
      .createHmac('sha256', process.env.AUDIT_SECRET || 'default-secret')
      .update(content)
      .digest('hex');
  },

  async verifyIntegrity(logId: string): Promise<boolean> {
    const log = await strapi.entityService.findOne('api::audit-log.audit-log', logId);
    
    const expectedChecksum = this.generateChecksum(log);
    return log.checksum === expectedChecksum;
  },

  async archiveOldLogs() {
    const retentionDays = parseInt(process.env.AUDIT_RETENTION_DAYS || '365');
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const oldLogs = await strapi.db.query('api::audit-log.audit-log').findMany({
      where: {
        createdAt: {
          $lt: cutoffDate,
        },
      },
      limit: 1000,
    });

    if (oldLogs.length > 0) {
      // Archive to external storage
      await this.archiveToS3(oldLogs);
      
      // Delete from database
      await strapi.db.query('api::audit-log.audit-log').deleteMany({
        where: {
          id: {
            $in: oldLogs.map(log => log.id),
          },
        },
      });
    }
  },

  async getAuditReport(startDate: Date, endDate: Date, filters: any = {}) {
    const logs = await strapi.db.query('api::audit-log.audit-log').findMany({
      where: {
        createdAt: {
          $gte: startDate,
          $lte: endDate,
        },
        ...filters,
      },
      orderBy: { createdAt: 'desc' },
    });

    return {
      period: { startDate, endDate },
      totalEvents: logs.length,
      eventsByAction: this.groupBy(logs, 'action'),
      eventsBySeverity: this.groupBy(logs, 'severity'),
      criticalEvents: logs.filter(log => log.severity === 'critical'),
      failedAttempts: logs.filter(log => !log.success),
    };
  },

  groupBy(array: any[], key: string) {
    return array.reduce((acc, item) => {
      const value = item[key];
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {});
  },
});
```

---

## 🛠️ Week 3-4: GDPR Implementation

### 5. GDPR Plugin Development

**Plugin Structure**:
```
backend/src/plugins/gdpr/
├── admin/
│   └── src/
│       ├── components/
│       │   ├── DataExport/
│       │   ├── ConsentManager/
│       │   └── AuditViewer/
│       └── pages/
├── server/
│   ├── controllers/
│   │   ├── data-export.ts
│   │   ├── data-deletion.ts
│   │   ├── consent.ts
│   │   └── audit.ts
│   ├── services/
│   │   ├── anonymization.ts
│   │   ├── encryption.ts
│   │   ├── export.ts
│   │   └── retention.ts
│   ├── routes/
│   ├── policies/
│   ├── middlewares/
│   └── bootstrap.ts
├── package.json
└── strapi-plugin.json
```

**Data Export Controller** (`backend/src/plugins/gdpr/server/controllers/data-export.ts`):
```typescript
import { factories } from '@strapi/strapi';

export default factories.createCoreController('plugin::gdpr.data-export', ({ strapi }) => ({
  async exportUserData(ctx) {
    const { userId } = ctx.params;
    const requestingUser = ctx.state.user;

    // Verify permissions
    if (requestingUser.id !== userId && !requestingUser.isAdmin) {
      return ctx.forbidden('You can only export your own data');
    }

    // Log the export request
    await strapi.service('api::audit-log.audit-log').create({
      action: 'data_request',
      entity: 'user',
      entityId: userId,
      userId: requestingUser.id,
      userEmail: requestingUser.email,
      ipAddress: ctx.request.ip,
      userAgent: ctx.request.header['user-agent'],
      metadata: { type: 'export' },
    });

    try {
      // Gather all user data
      const exportService = strapi.plugin('gdpr').service('export');
      const userData = await exportService.gatherUserData(userId);

      // Generate export file
      const exportFile = await exportService.generateExport(userData, ctx.query.format || 'json');

      // Send file
      ctx.set('Content-Type', exportFile.contentType);
      ctx.set('Content-Disposition', `attachment; filename="user-data-${userId}-${Date.now()}.${exportFile.extension}"`);
      
      return exportFile.data;
    } catch (error) {
      strapi.log.error('Data export failed:', error);
      return ctx.internalServerError('Export failed');
    }
  },
}));
```

**Data Export Service** (`backend/src/plugins/gdpr/server/services/export.ts`):
```typescript
import { format } from 'date-fns';

export default ({ strapi }) => ({
  async gatherUserData(userId: string) {
    const userData = {
      exportDate: new Date().toISOString(),
      dataSubject: {
        id: userId,
        requestDate: new Date().toISOString(),
      },
      personalData: {},
      activityData: {},
      consentHistory: [],
      dataProcessing: [],
    };

    // Get user account data
    const user = await strapi.entityService.findOne(
      'plugin::users-permissions.user',
      userId,
      {
        populate: {
          role: true,
          location: true,
          zones: true,
        },
      }
    );

    if (!user) {
      throw new Error('User not found');
    }

    // Personal identifiable information
    userData.personalData = {
      account: {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        confirmed: user.confirmed,
        blocked: user.blocked,
        role: user.role?.name,
      },
      location: user.location ? {
        address: user.location.address,
        postalCode: user.location.postalCode,
        city: user.location.city,
        country: user.location.country,
        coordinates: user.location.coordinates,
      } : null,
      preferences: {
        language: user.language,
        timezone: user.timezone,
        notifications: user.notificationPreferences,
      },
    };

    // Get activity data (Phase 1 will add poll responses)
    userData.activityData = {
      lastLogin: user.lastLogin,
      loginCount: user.loginCount || 0,
      // Placeholder for future activity data
      polls: [],
      responses: [],
    };

    // Get consent history
    const consents = await strapi.db.query('plugin::gdpr.consent').findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });

    userData.consentHistory = consents.map(consent => ({
      type: consent.type,
      granted: consent.granted,
      timestamp: consent.createdAt,
      ip: consent.ipAddress,
      version: consent.version,
    }));

    // Get audit logs for transparency
    const auditLogs = await strapi.db.query('api::audit-log.audit-log').findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      limit: 100,
    });

    userData.dataProcessing = auditLogs.map(log => ({
      action: log.action,
      timestamp: log.createdAt,
      entity: log.entity,
      success: log.success,
    }));

    return userData;
  },

  async generateExport(data: any, format: string = 'json') {
    switch (format.toLowerCase()) {
      case 'json':
        return {
          data: Buffer.from(JSON.stringify(data, null, 2)),
          contentType: 'application/json',
          extension: 'json',
        };

      case 'csv':
        // Flatten data for CSV
        const csvData = this.flattenForCSV(data);
        return {
          data: Buffer.from(csvData),
          contentType: 'text/csv',
          extension: 'csv',
        };

      case 'pdf':
        // Generate PDF report
        const pdfData = await this.generatePDFReport(data);
        return {
          data: pdfData,
          contentType: 'application/pdf',
          extension: 'pdf',
        };

      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  },

  flattenForCSV(data: any): string {
    // Implementation of CSV flattening
    const lines = ['Category,Field,Value'];
    
    const addLine = (category: string, field: string, value: any) => {
      const escapedValue = String(value).replace(/"/g, '""');
      lines.push(`"${category}","${field}","${escapedValue}"`);
    };

    // Flatten personal data
    Object.entries(data.personalData.account).forEach(([key, value]) => {
      addLine('Personal Data', key, value);
    });

    if (data.personalData.location) {
      Object.entries(data.personalData.location).forEach(([key, value]) => {
        addLine('Location', key, value);
      });
    }

    // Add consent history
    data.consentHistory.forEach((consent, index) => {
      addLine('Consent History', `Consent ${index + 1}`, 
        `${consent.type}: ${consent.granted ? 'Granted' : 'Denied'} at ${consent.timestamp}`);
    });

    return lines.join('\n');
  },
});
```

**Anonymization Service** (`backend/src/plugins/gdpr/server/services/anonymization.ts`):
```typescript
import crypto from 'crypto';

export default ({ strapi }) => ({
  async anonymizeUser(userId: string, options = {}) {
    const { 
      keepAnalytics = true, 
      deleteContent = false,
      reason = 'user_request' 
    } = options;

    // Log anonymization request
    await strapi.service('api::audit-log.audit-log').create({
      action: 'data_deletion',
      entity: 'user',
      entityId: userId,
      metadata: { reason, keepAnalytics, deleteContent },
      severity: 'high',
    });

    // Generate anonymous identifiers
    const anonId = crypto.randomBytes(16).toString('hex');
    const timestamp = Date.now();

    try {
      // Anonymize user data
      await strapi.entityService.update(
        'plugin::users-permissions.user',
        userId,
        {
          data: {
            email: `anon_${anonId}@deleted.local`,
            username: `deleted_user_${timestamp}`,
            firstName: 'Deleted',
            lastName: 'User',
            password: crypto.randomBytes(32).toString('hex'), // Random unusable password
            confirmed: false,
            blocked: true,
            provider: 'local',
            // Clear personal data
            phone: null,
            dateOfBirth: null,
            avatar: null,
            bio: null,
            // Clear location data
            location: null,
            zones: [],
            // Metadata
            anonymizedAt: new Date(),
            anonymizationReason: reason,
          },
        }
      );

      // Handle related data based on options
      if (keepAnalytics) {
        // Keep aggregated data but remove personal identifiers
        await this.anonymizeRelatedData(userId);
      } else if (deleteContent) {
        // Complete deletion of all related content
        await this.deleteAllUserContent(userId);
      }

      // Clear sessions and tokens
      await this.clearUserSessions(userId);

      // Schedule permanent deletion after retention period
      await this.schedulePermanentDeletion(userId);

      return {
        success: true,
        anonymizedId: anonId,
        timestamp,
      };
    } catch (error) {
      strapi.log.error('Anonymization failed:', error);
      throw error;
    }
  },

  async anonymizeRelatedData(userId: string) {
    // This will be extended in future phases
    // For now, just clear personal references in audit logs
    await strapi.db.query('api::audit-log.audit-log').updateMany({
      where: { userId },
      data: {
        userEmail: '<EMAIL>',
        metadata: strapi.db.connection.raw(
          "jsonb_set(metadata, '{anonymized}', 'true'::jsonb)"
        ),
      },
    });
  },

  async clearUserSessions(userId: string) {
    // Clear all active sessions
    // Implementation depends on session storage
  },

  async schedulePermanentDeletion(userId: string, days = 30) {
    const deletionDate = new Date();
    deletionDate.setDate(deletionDate.getDate() + days);

    await strapi.entityService.create('plugin::gdpr.deletion-request', {
      data: {
        userId,
        scheduledFor: deletionDate,
        status: 'pending',
      },
    });
  },
});
```

**Consent Management** (`backend/src/plugins/gdpr/server/content-types/consent/schema.json`):
```json
{
  "kind": "collectionType",
  "collectionName": "gdpr_consents",
  "info": {
    "singularName": "consent",
    "pluralName": "consents",
    "displayName": "GDPR Consent",
    "description": "User consent records for GDPR compliance"
  },
  "options": {
    "draftAndPublish": false,
    "timestamps": true
  },
  "attributes": {
    "userId": {
      "type": "string",
      "required": true
    },
    "type": {
      "type": "enumeration",
      "enum": [
        "terms_of_service",
        "privacy_policy",
        "marketing_emails",
        "analytics_tracking",
        "third_party_sharing",
        "cookies_essential",
        "cookies_analytics",
        "cookies_marketing"
      ],
      "required": true
    },
    "granted": {
      "type": "boolean",
      "required": true
    },
    "version": {
      "type": "string",
      "required": true
    },
    "ipAddress": {
      "type": "string",
      "required": true
    },
    "userAgent": {
      "type": "text"
    },
    "withdrawnAt": {
      "type": "datetime"
    },
    "expiresAt": {
      "type": "datetime"
    },
    "metadata": {
      "type": "json"
    }
  }
}
```

### 6. Encryption Service

**Encryption Service** (`backend/src/plugins/gdpr/server/services/encryption.ts`):
```typescript
import crypto from 'crypto';

export default ({ strapi }) => ({
  algorithm: 'aes-256-gcm',
  
  getKey(): Buffer {
    const key = process.env.ENCRYPTION_KEY;
    if (!key) {
      throw new Error('ENCRYPTION_KEY not set');
    }
    return Buffer.from(key, 'hex');
  },

  encrypt(text: string): { encrypted: string; iv: string; authTag: string } {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(this.algorithm, this.getKey(), iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex'),
    };
  },

  decrypt(encryptedData: { encrypted: string; iv: string; authTag: string }): string {
    const decipher = crypto.createDecipheriv(
      this.algorithm,
      this.getKey(),
      Buffer.from(encryptedData.iv, 'hex')
    );
    
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  },

  hashPII(data: string): string {
    return crypto
      .createHash('sha256')
      .update(data + process.env.PII_SALT)
      .digest('hex');
  },
});
```

---

## 🛠️ Week 5-6: Monitoring, Testing & Documentation

### 7. Monitoring Setup

**Prometheus Metrics Endpoint** (`backend/src/api/metrics/controllers/metrics.ts`):
```typescript
import { Registry, Counter, Histogram, Gauge } from 'prom-client';

const register = new Registry();

// Define metrics
const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status'],
  registers: [register],
});

const httpRequestTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status'],
  registers: [register],
});

const activeUsers = new Gauge({
  name: 'active_users',
  help: 'Number of active users',
  registers: [register],
});

const databaseConnections = new Gauge({
  name: 'database_connections',
  help: 'Number of active database connections',
  registers: [register],
});

const auditLogEntries = new Counter({
  name: 'audit_log_entries_total',
  help: 'Total number of audit log entries',
  labelNames: ['action', 'severity'],
  registers: [register],
});

export default {
  async metrics(ctx) {
    try {
      // Update metrics
      const pool = strapi.db.connection.context.client.pool;
      databaseConnections.set(pool.numUsed());

      // Get active users count
      const activeSessions = await strapi.db.query('plugin::users-permissions.user').count({
        where: {
          lastLogin: {
            $gte: new Date(Date.now() - 15 * 60 * 1000), // Last 15 minutes
          },
        },
      });
      activeUsers.set(activeSessions);

      // Return metrics
      ctx.set('Content-Type', register.contentType);
      ctx.body = await register.metrics();
    } catch (error) {
      ctx.throw(500, error);
    }
  },
};

// Export middleware for tracking requests
export const metricsMiddleware = () => {
  return async (ctx, next) => {
    const start = Date.now();
    
    try {
      await next();
    } finally {
      const duration = (Date.now() - start) / 1000;
      const route = ctx.request.route?.path || 'unknown';
      const method = ctx.request.method;
      const status = ctx.response.status;

      httpRequestDuration.observe(
        { method, route, status },
        duration
      );

      httpRequestTotal.inc({
        method,
        route,
        status,
      });

      // Track audit events
      if (ctx.state.auditLog) {
        auditLogEntries.inc({
          action: ctx.state.auditLog.action,
          severity: ctx.state.auditLog.severity || 'low',
        });
      }
    }
  };
};
```

### 8. Backup System

**Backup Script** (`scripts/backup.sh`):
```bash
#!/bin/bash

# Backup script for CivicPoll
# This script performs encrypted backups of database and files

set -euo pipefail

# Configuration
BACKUP_DIR="/opt/civicpoll/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30
DB_NAME="civicpoll_fr"
DB_USER="civicpoll_user"
PGPASSWORD="${DATABASE_PASSWORD}"
ENCRYPTION_KEY="${BACKUP_ENCRYPTION_KEY}"

# Ensure backup directory exists
mkdir -p "${BACKUP_DIR}/{db,files,logs}"

# Function to log messages
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "${BACKUP_DIR}/logs/backup_${TIMESTAMP}.log"
}

# Function to encrypt file
encrypt_file() {
    local input_file=$1
    local output_file="${input_file}.enc"
    
    openssl enc -aes-256-cbc -salt -in "${input_file}" -out "${output_file}" -k "${ENCRYPTION_KEY}"
    rm "${input_file}"
    echo "${output_file}"
}

# Start backup process
log "Starting backup process..."

# 1. Database backup
log "Backing up database..."
DB_BACKUP_FILE="${BACKUP_DIR}/db/civicpoll_db_${TIMESTAMP}.sql"

export PGPASSWORD
pg_dump -h localhost -U "${DB_USER}" -d "${DB_NAME}" \
    --no-owner --no-privileges --clean --if-exists \
    > "${DB_BACKUP_FILE}"

# Compress database backup
gzip "${DB_BACKUP_FILE}"
DB_BACKUP_FILE="${DB_BACKUP_FILE}.gz"

# Encrypt database backup
DB_BACKUP_FILE=$(encrypt_file "${DB_BACKUP_FILE}")
log "Database backup completed: ${DB_BACKUP_FILE}"

# 2. Files backup
log "Backing up files..."
FILES_BACKUP_FILE="${BACKUP_DIR}/files/civicpoll_files_${TIMESTAMP}.tar.gz"

tar -czf "${FILES_BACKUP_FILE}" \
    -C /opt/civicpoll/backend \
    public/uploads \
    config/env \
    .env \
    --exclude='*.log' \
    --exclude='node_modules' \
    --exclude='.cache'

# Encrypt files backup
FILES_BACKUP_FILE=$(encrypt_file "${FILES_BACKUP_FILE}")
log "Files backup completed: ${FILES_BACKUP_FILE}"

# 3. Audit logs export
log "Exporting audit logs..."
AUDIT_EXPORT_FILE="${BACKUP_DIR}/logs/audit_export_${TIMESTAMP}.json"

# Export audit logs via Strapi API
curl -X GET "http://localhost:1337/api/audit-logs/export" \
    -H "Authorization: Bearer ${BACKUP_API_TOKEN}" \
    -o "${AUDIT_EXPORT_FILE}"

# Compress and encrypt audit logs
gzip "${AUDIT_EXPORT_FILE}"
AUDIT_EXPORT_FILE="${AUDIT_EXPORT_FILE}.gz"
AUDIT_EXPORT_FILE=$(encrypt_file "${AUDIT_EXPORT_FILE}")
log "Audit logs export completed: ${AUDIT_EXPORT_FILE}"

# 4. Create backup manifest
MANIFEST_FILE="${BACKUP_DIR}/manifest_${TIMESTAMP}.json"
cat > "${MANIFEST_FILE}" << EOF
{
  "timestamp": "${TIMESTAMP}",
  "version": "1.0",
  "files": {
    "database": "$(basename ${DB_BACKUP_FILE})",
    "files": "$(basename ${FILES_BACKUP_FILE})",
    "audit": "$(basename ${AUDIT_EXPORT_FILE})"
  },
  "checksums": {
    "database": "$(sha256sum ${DB_BACKUP_FILE} | cut -d' ' -f1)",
    "files": "$(sha256sum ${FILES_BACKUP_FILE} | cut -d' ' -f1)",
    "audit": "$(sha256sum ${AUDIT_EXPORT_FILE} | cut -d' ' -f1)"
  }
}
EOF

# 5. Upload to S3 (if configured)
if [ ! -z "${AWS_BACKUP_BUCKET:-}" ]; then
    log "Uploading to S3..."
    aws s3 cp "${DB_BACKUP_FILE}" "s3://${AWS_BACKUP_BUCKET}/civicpoll-fr/db/"
    aws s3 cp "${FILES_BACKUP_FILE}" "s3://${AWS_BACKUP_BUCKET}/civicpoll-fr/files/"
    aws s3 cp "${AUDIT_EXPORT_FILE}" "s3://${AWS_BACKUP_BUCKET}/civicpoll-fr/logs/"
    aws s3 cp "${MANIFEST_FILE}" "s3://${AWS_BACKUP_BUCKET}/civicpoll-fr/manifests/"
    log "S3 upload completed"
fi

# 6. Cleanup old backups
log "Cleaning up old backups..."
find "${BACKUP_DIR}/db" -name "*.enc" -mtime +${RETENTION_DAYS} -delete
find "${BACKUP_DIR}/files" -name "*.enc" -mtime +${RETENTION_DAYS} -delete
find "${BACKUP_DIR}/logs" -name "*.enc" -mtime +${RETENTION_DAYS} -delete

# 7. Verify backup integrity
log "Verifying backup integrity..."
./scripts/verify-backup.sh "${MANIFEST_FILE}"

log "Backup process completed successfully!"

# Send notification
if [ ! -z "${BACKUP_NOTIFICATION_WEBHOOK:-}" ]; then
    curl -X POST "${BACKUP_NOTIFICATION_WEBHOOK}" \
        -H "Content-Type: application/json" \
        -d "{\"text\": \"CivicPoll backup completed successfully\", \"timestamp\": \"${TIMESTAMP}\"}"
fi
```

### 9. Testing Suite

**Security Tests** (`backend/tests/security/security.test.ts`):
```typescript
import request from 'supertest';
import { setupStrapi, teardownStrapi } from '../helpers/strapi';

describe('Security Tests', () => {
  let strapi: any;

  beforeAll(async () => {
    strapi = await setupStrapi();
  });

  afterAll(async () => {
    await teardownStrapi(strapi);
  });

  describe('Security Headers', () => {
    it('should set all required security headers', async () => {
      const response = await request(strapi.server.httpServer)
        .get('/api/health')
        .expect(200);

      expect(response.headers['x-frame-options']).toBe('SAMEORIGIN');
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['strict-transport-security']).toContain('max-age=31536000');
      expect(response.headers['content-security-policy']).toBeDefined();
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      const requests = [];
      
      // Make 101 requests (limit is 100)
      for (let i = 0; i < 101; i++) {
        requests.push(
          request(strapi.server.httpServer)
            .get('/api/health')
        );
      }

      const responses = await Promise.all(requests);
      const rateLimited = responses.filter(r => r.status === 429);
      
      expect(rateLimited.length).toBeGreaterThan(0);
    });
  });

  describe('SQL Injection Prevention', () => {
    it('should block SQL injection attempts', async () => {
      const response = await request(strapi.server.httpServer)
        .get('/api/users?filters[email]=<EMAIL>; DROP TABLE users;--')
        .expect(400);

      expect(response.body.error).toBeDefined();
    });
  });

  describe('Input Validation', () => {
    it('should reject invalid content types', async () => {
      const response = await request(strapi.server.httpServer)
        .post('/api/audit-logs')
        .set('Content-Type', 'text/plain')
        .send('invalid data')
        .expect(400);

      expect(response.body.error.message).toContain('Content-Type must be application/json');
    });

    it('should reject oversized requests', async () => {
      const largePayload = 'x'.repeat(11 * 1024 * 1024); // 11MB
      
      const response = await request(strapi.server.httpServer)
        .post('/api/audit-logs')
        .set('Content-Type', 'application/json')
        .send({ data: largePayload })
        .expect(413);

      expect(response.body.error.message).toContain('Request entity too large');
    });
  });
});
```

**GDPR Compliance Tests** (`backend/tests/gdpr/gdpr.test.ts`):
```typescript
describe('GDPR Compliance Tests', () => {
  describe('Data Export', () => {
    it('should export all user data', async () => {
      const user = await createTestUser();
      
      const response = await request(strapi.server.httpServer)
        .get(`/gdpr/export/${user.id}`)
        .set('Authorization', `Bearer ${user.jwt}`)
        .expect(200);

      const exportData = JSON.parse(response.text);
      
      expect(exportData).toHaveProperty('personalData');
      expect(exportData).toHaveProperty('activityData');
      expect(exportData).toHaveProperty('consentHistory');
      expect(exportData.personalData.account.email).toBe(user.email);
    });

    it('should not allow exporting other users data', async () => {
      const user1 = await createTestUser();
      const user2 = await createTestUser();
      
      await request(strapi.server.httpServer)
        .get(`/gdpr/export/${user2.id}`)
        .set('Authorization', `Bearer ${user1.jwt}`)
        .expect(403);
    });
  });

  describe('Data Deletion', () => {
    it('should anonymize user data on deletion request', async () => {
      const user = await createTestUser();
      
      await request(strapi.server.httpServer)
        .delete(`/gdpr/delete/${user.id}`)
        .set('Authorization', `Bearer ${user.jwt}`)
        .expect(200);

      const anonymizedUser = await strapi.entityService.findOne(
        'plugin::users-permissions.user',
        user.id
      );

      expect(anonymizedUser.email).toMatch(/anon_.*@deleted\.local/);
      expect(anonymizedUser.username).toMatch(/deleted_user_.*/);
      expect(anonymizedUser.blocked).toBe(true);
    });
  });

  describe('Consent Management', () => {
    it('should track consent changes', async () => {
      const user = await createTestUser();
      
      await request(strapi.server.httpServer)
        .post('/gdpr/consent')
        .set('Authorization', `Bearer ${user.jwt}`)
        .send({
          type: 'marketing_emails',
          granted: true,
          version: '1.0',
        })
        .expect(200);

      const consents = await strapi.db.query('plugin::gdpr.consent').findMany({
        where: { userId: user.id },
      });

      expect(consents).toHaveLength(1);
      expect(consents[0].type).toBe('marketing_emails');
      expect(consents[0].granted).toBe(true);
    });
  });

  describe('Audit Trail', () => {
    it('should log all data access', async () => {
      const user = await createTestUser();
      
      // Export data (which should create audit log)
      await request(strapi.server.httpServer)
        .get(`/gdpr/export/${user.id}`)
        .set('Authorization', `Bearer ${user.jwt}`)
        .expect(200);

      const auditLogs = await strapi.db.query('api::audit-log.audit-log').findMany({
        where: {
          userId: user.id,
          action: 'data_request',
        },
      });

      expect(auditLogs).toHaveLength(1);
      expect(auditLogs[0].entity).toBe('user');
    });
  });
});
```

### 10. CI/CD Pipeline

**.gitlab-ci.yml**:
```yaml
stages:
  - security
  - test
  - build
  - deploy

variables:
  NODE_VERSION: "20"
  POSTGRES_VERSION: "15"

# Security scanning stage
security:dependencies:
  stage: security
  image: node:${NODE_VERSION}
  script:
    - cd backend
    - npm ci
    - npm audit --audit-level=moderate
    - npx snyk test
  allow_failure: false

security:sast:
  stage: security
  image: 
    name: "registry.gitlab.com/gitlab-org/security-products/sast:latest"
  script:
    - /analyzer run
  artifacts:
    reports:
      sast: gl-sast-report.json

security:secrets:
  stage: security
  image: trufflesecurity/trufflehog:latest
  script:
    - trufflehog git file://. --only-verified

# Testing stage
test:unit:
  stage: test
  image: node:${NODE_VERSION}
  services:
    - postgres:${POSTGRES_VERSION}
  variables:
    POSTGRES_DB: civicpoll_test
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_pass
    DATABASE_HOST: postgres
  script:
    - cd backend
    - npm ci
    - npm run test:unit
  coverage: '/Coverage: \d+\.\d+%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: backend/coverage/cobertura-coverage.xml

test:integration:
  stage: test
  image: node:${NODE_VERSION}
  services:
    - postgres:${POSTGRES_VERSION}
    - redis:7-alpine
  variables:
    POSTGRES_DB: civicpoll_test
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_pass
    DATABASE_HOST: postgres
    REDIS_HOST: redis
  script:
    - cd backend
    - npm ci
    - npm run test:integration

test:security:
  stage: test
  image: node:${NODE_VERSION}
  services:
    - postgres:${POSTGRES_VERSION}
  script:
    - cd backend
    - npm ci
    - npm run test:security

test:gdpr:
  stage: test
  image: node:${NODE_VERSION}
  services:
    - postgres:${POSTGRES_VERSION}
  script:
    - cd backend
    - npm ci
    - npm run test:gdpr

# Build stage
build:backend:
  stage: build
  image: node:${NODE_VERSION}
  script:
    - cd backend
    - npm ci --production
    - npm run build
  artifacts:
    paths:
      - backend/build/
      - backend/public/
    expire_in: 1 week

# Documentation
pages:
  stage: build
  image: node:${NODE_VERSION}
  script:
    - cd docs
    - npm ci
    - npm run build
    - mv build ../public
  artifacts:
    paths:
      - public
  only:
    - main
```

---

## 📋 Development Checklist

### Security Implementation ✓
- [ ] Strapi v5 with TypeScript configured
- [ ] PostgreSQL with PostGIS enabled
- [ ] SSL/TLS configuration
- [ ] Security headers middleware
- [ ] Rate limiting implemented
- [ ] Input validation and sanitization
- [ ] SQL injection prevention
- [ ] XSS protection

### GDPR Compliance ✓
- [ ] GDPR plugin developed
- [ ] Data export functionality
- [ ] Data anonymization service
- [ ] Consent management system
- [ ] Audit trail implementation
- [ ] Retention policies configured
- [ ] Encryption service for PII
- [ ] Right to deletion implemented

### Monitoring & Backup ✓
- [ ] Prometheus metrics endpoint
- [ ] Grafana dashboards configured
- [ ] Automated backup system
- [ ] Backup encryption
- [ ] Backup verification
- [ ] Monitoring alerts setup

### Testing ✓
- [ ] Security test suite
- [ ] GDPR compliance tests
- [ ] Integration tests
- [ ] Performance tests
- [ ] Load testing scenarios
- [ ] Test coverage > 80%

### Documentation ✓
- [ ] API documentation
- [ ] Security procedures
- [ ] GDPR compliance guide
- [ ] Admin user guide
- [ ] Backup/restore procedures
- [ ] Incident response plan

### CI/CD ✓
- [ ] GitLab CI pipeline
- [ ] Security scanning
- [ ] Automated testing
- [ ] Code quality checks
- [ ] Deployment automation
- [ ] Rollback procedures

---

## 🚀 Phase 0 Completion Criteria

Before proceeding to Phase 1, ensure:

1. **Security Audit**: 
   - SSL Labs score: A minimum
   - No critical vulnerabilities in dependency scan
   - All security headers properly configured

2. **GDPR Compliance**:
   - All data export/import functions tested
   - Consent management operational
   - Audit logs capturing all required events

3. **Performance Baseline**:
   - API response time < 200ms
   - Database queries optimized
   - Caching strategy implemented

4. **Operational Readiness**:
   - Backup system tested with successful restore
   - Monitoring dashboards showing all metrics
   - Alert system tested and working

5. **Documentation Complete**:
   - All procedures documented
   - Admin trained on all systems
   - Runbooks for common scenarios

---

## 📞 Support & Escalation

During Phase 0 development:

- **Technical Issues**: Create detailed bug reports with logs
- **Security Concerns**: Immediate escalation to security team
- **GDPR Questions**: Consult with legal/compliance team
- **Infrastructure**: DevOps team for server/network issues

---

*Phase 0 Development Plan - CivicPoll - Version 1.0*